{"timestamp": "2025-07-22T13:40:21.527143", "basic_info": {"hostname": "DESKTOP-SJB52A0", "platform": "Windows-10-10.0.19041-SP0", "system": "Windows", "release": "10", "version": "10.0.19041", "machine": "AMD64", "processor": "Intel64 Family 6 Model 37 Stepping 5, GenuineIntel", "architecture": ["64bit", "WindowsPE"], "python_version": "3.13.5 (tags/v3.13.5:6cb20a2, Jun 11 2025, 16:15:46) [MSC v.1943 64 bit (AMD64)]", "python_executable": "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe", "current_user": "TOONOO", "current_directory": "C:\\Users\\<USER>\\Desktop\\Keylogger-Script", "home_directory": "C:\\Users\\<USER>"}, "hardware_info": {"cpu": {"physical_cores": 2, "logical_cores": 4, "cpu_frequency": {"current": 3200.0, "min": 0.0, "max": 3200.0}, "cpu_usage_percent": 29.6}, "memory": {"total": 7171325952, "available": 1331519488, "used": 5839806464, "percentage": 81.4}, "disks": [{"device": "C:\\", "mountpoint": "C:\\", "filesystem": "NTFS", "total": 301803249664, "used": 55577350144, "free": 246225899520, "percentage": 18.415093345043406}, {"device": "F:\\", "mountpoint": "F:\\", "filesystem": "NTFS", "total": 209714147328, "used": 2656423936, "free": 207057723392, "percentage": 1.266687998804994}, {"device": "G:\\", "mountpoint": "G:\\", "filesystem": "FAT", "total": 1999437824, "used": 250970112, "free": 1748467712, "percentage": 12.552033826084106}]}, "network_info": {"interfaces": [{"name": "Ethernet", "addresses": [{"family": "-1", "address": "E6-77-E9-12-7F-C3", "netmask": null, "broadcast": null}, {"family": "2", "address": "***************", "netmask": "***********", "broadcast": null}, {"family": "2", "address": "**************", "netmask": "*************", "broadcast": null}, {"family": "23", "address": "fe80::f05e:663e:e907:6b94", "netmask": null, "broadcast": null}]}, {"name": "Ethernet 4", "addresses": [{"family": "-1", "address": "02-00-42-4C-39-51", "netmask": null, "broadcast": null}, {"family": "2", "address": "**************", "netmask": "*************", "broadcast": null}, {"family": "23", "address": "fe80::3deb:316c:1b90:145c", "netmask": null, "broadcast": null}]}, {"name": "Ethernet 2", "addresses": [{"family": "-1", "address": "00-A1-B0-D6-14-82", "netmask": null, "broadcast": null}, {"family": "2", "address": "**************", "netmask": "***********", "broadcast": null}, {"family": "23", "address": "fe80::3521:6558:598e:1494", "netmask": null, "broadcast": null}]}, {"name": "Loopback Pseudo-Interface 1", "addresses": [{"family": "2", "address": "127.0.0.1", "netmask": "*********", "broadcast": null}, {"family": "23", "address": "::1", "netmask": null, "broadcast": null}]}], "statistics": {"bytes_sent": 67744919, "bytes_received": 368238109, "packets_sent": 192891, "packets_received": 326430}, "active_connections": [{"family": "2", "type": "1", "local_address": "**************:57967", "remote_address": "***********:443", "status": "ESTABLISHED", "pid": 23640}, {"family": "2", "type": "1", "local_address": "**************:57899", "remote_address": "************:443", "status": "ESTABLISHED", "pid": 6828}, {"family": "2", "type": "1", "local_address": "**************:57919", "remote_address": "***********:443", "status": "TIME_WAIT", "pid": 0}, {"family": "2", "type": "1", "local_address": "**************:57946", "remote_address": "**************:80", "status": "TIME_WAIT", "pid": 0}, {"family": "23", "type": "1", "local_address": ":::49664", "remote_address": null, "status": "LISTEN", "pid": 796}, {"family": "23", "type": "1", "local_address": ":::135", "remote_address": null, "status": "LISTEN", "pid": 616}, {"family": "2", "type": "1", "local_address": "**************:57930", "remote_address": "***********:443", "status": "TIME_WAIT", "pid": 0}, {"family": "2", "type": "1", "local_address": "**************:57920", "remote_address": "***********:443", "status": "TIME_WAIT", "pid": 0}, {"family": "2", "type": "1", "local_address": "**************:57965", "remote_address": "***********:443", "status": "TIME_WAIT", "pid": 0}, {"family": "2", "type": "1", "local_address": "**************:57904", "remote_address": "***********:443", "status": "TIME_WAIT", "pid": 0}]}, "software_info": {}, "process_info": {}, "environment_info": {"environment_variables": {"PATH": "c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Windows\\system32;C:\\Windows;C:\\Windows\\System32\\Wbem;C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\;C:\\Windows\\System32\\OpenSSH\\;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Program Files\\dotnet\\;C:\\Program Files\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Scripts\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Launcher\\;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;c:\\Users\\<USER>\\AppData\\Roaming\\Code\\User\\globalStorage\\github.copilot-chat\\debugCommand;c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.debugpy-2025.10.0-win32-x64\\bundled\\scripts\\noConfigScripts", "USERNAME": "TOONOO", "COMPUTERNAME": "DESKTOP-SJB52A0", "OS": "Windows_NT", "PROCESSOR_ARCHITECTURE": "AMD64", "TEMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "TMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "APPDATA": "C:\\Users\\<USER>\\AppData\\Roaming", "LOCALAPPDATA": "C:\\Users\\<USER>\\AppData\\Local"}, "timezone": {"timezone": ["Romance Standard Time", "Romance Standard Time"], "utc_offset": -3600, "daylight_saving": 0}, "locale": {"encoding": "utf-8", "filesystem_encoding": "utf-8"}}}