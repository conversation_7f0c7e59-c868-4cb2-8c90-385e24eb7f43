"""
Encryption utilities for the Advanced Keylogger.
"""

import os
import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from typing import Optional

class EncryptionManager:
    """Handles encryption and decryption of keylogger data."""
    
    def __init__(self, key_file: str = "encryption.key", password: Optional[str] = None):
        """
        Initialize encryption manager.
        
        Args:
            key_file: Path to the encryption key file
            password: Optional password for key derivation
        """
        self.key_file = key_file
        self.password = password
        self.cipher = None
        self._load_or_generate_key()
    
    def _load_or_generate_key(self) -> None:
        """Load existing key or generate a new one."""
        if os.path.exists(self.key_file):
            self._load_key()
        else:
            self._generate_key()
    
    def _generate_key(self) -> None:
        """Generate a new encryption key."""
        if self.password:
            # Derive key from password
            salt = os.urandom(16)
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(self.password.encode()))
            
            # Save salt and key
            with open(self.key_file, 'wb') as f:
                f.write(salt + key)
        else:
            # Generate random key
            key = Fernet.generate_key()
            with open(self.key_file, 'wb') as f:
                f.write(key)
        
        self._load_key()
    
    def _load_key(self) -> None:
        """Load encryption key from file."""
        with open(self.key_file, 'rb') as f:
            key_data = f.read()
        
        if self.password and len(key_data) > 32:
            # Extract salt and derive key
            salt = key_data[:16]
            stored_key = key_data[16:]
            
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            derived_key = base64.urlsafe_b64encode(kdf.derive(self.password.encode()))
            
            if derived_key != stored_key:
                raise ValueError("Invalid password")
            
            self.cipher = Fernet(derived_key)
        else:
            # Direct key
            self.cipher = Fernet(key_data)
    
    def encrypt(self, data: str) -> bytes:
        """
        Encrypt string data.
        
        Args:
            data: String to encrypt
            
        Returns:
            Encrypted bytes
        """
        if not self.cipher:
            raise RuntimeError("Encryption not initialized")
        
        return self.cipher.encrypt(data.encode('utf-8'))
    
    def decrypt(self, encrypted_data: bytes) -> str:
        """
        Decrypt bytes data.
        
        Args:
            encrypted_data: Encrypted bytes
            
        Returns:
            Decrypted string
        """
        if not self.cipher:
            raise RuntimeError("Encryption not initialized")
        
        return self.cipher.decrypt(encrypted_data).decode('utf-8')
    
    def encrypt_file(self, input_file: str, output_file: str) -> None:
        """
        Encrypt a file.
        
        Args:
            input_file: Path to input file
            output_file: Path to output encrypted file
        """
        with open(input_file, 'rb') as f:
            data = f.read()
        
        encrypted_data = self.cipher.encrypt(data)
        
        with open(output_file, 'wb') as f:
            f.write(encrypted_data)
    
    def decrypt_file(self, input_file: str, output_file: str) -> None:
        """
        Decrypt a file.
        
        Args:
            input_file: Path to encrypted input file
            output_file: Path to output decrypted file
        """
        with open(input_file, 'rb') as f:
            encrypted_data = f.read()
        
        decrypted_data = self.cipher.decrypt(encrypted_data)
        
        with open(output_file, 'wb') as f:
            f.write(decrypted_data)
    
    def get_key_fingerprint(self) -> str:
        """
        Get a fingerprint of the encryption key.

        Returns:
            Key fingerprint as hex string
        """
        if not self.cipher:
            return "No key loaded"

        try:
            # Create a simple fingerprint from the key file
            import hashlib
            with open(self.key_file, 'rb') as f:
                key_data = f.read()

            # Create SHA256 hash of the key data
            hash_obj = hashlib.sha256(key_data)
            return hash_obj.hexdigest()[:16]
        except Exception:
            return "Key fingerprint unavailable"
