"""
Logging utilities for the Advanced Keylogger.
"""

import os
import logging
import logging.handlers
from datetime import datetime
from typing import Optional

def setup_logger(
    name: str = "keylogger",
    log_file: Optional[str] = None,
    level: int = logging.INFO,
    max_bytes: int = 10485760,  # 10MB
    backup_count: int = 5
) -> logging.Logger:
    """
    Set up a logger with file rotation and console output.
    
    Args:
        name: Logger name
        log_file: Log file path (optional)
        level: Logging level
        max_bytes: Maximum log file size before rotation
        backup_count: Number of backup files to keep
        
    Returns:
        Configured logger instance
    """
    logger = logging.getLogger(name)
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
    
    logger.setLevel(level)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler with rotation
    if log_file:
        # Ensure log directory exists
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
        
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def get_session_logger(session_id: str, log_dir: str = "logs") -> logging.Logger:
    """
    Create a session-specific logger.
    
    Args:
        session_id: Unique session identifier
        log_dir: Directory for log files
        
    Returns:
        Session logger instance
    """
    log_file = os.path.join(log_dir, f"session_{session_id}.log")
    return setup_logger(f"session_{session_id}", log_file)

def create_session_id() -> str:
    """
    Create a unique session identifier.
    
    Returns:
        Session ID string
    """
    return datetime.now().strftime("%Y%m%d_%H%M%S")

class KeyloggerLogger:
    """Custom logger for keylogger events."""
    
    def __init__(self, config):
        """
        Initialize keylogger logger.
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.session_id = create_session_id()
        
        # Setup main logger
        log_file = None
        if not config.stealth_mode:
            log_dir = config.log_directory
            os.makedirs(log_dir, exist_ok=True)
            log_file = os.path.join(log_dir, "keylogger.log")
        
        self.logger = setup_logger(
            "keylogger",
            log_file,
            logging.DEBUG if config.debug else logging.INFO
        )
        
        # Setup session logger
        if not config.stealth_mode:
            self.session_logger = get_session_logger(self.session_id, config.log_directory)
        else:
            self.session_logger = self.logger
    
    def log_keypress(self, key: str, window_title: str = None) -> None:
        """
        Log a keypress event.
        
        Args:
            key: The key that was pressed
            window_title: Title of the active window
        """
        if self.config.debug:
            message = f"Key: {key}"
            if window_title and self.config.get('logging.include_window_titles', True):
                message += f" | Window: {window_title}"
            self.session_logger.debug(message)
    
    def log_screenshot(self, filename: str) -> None:
        """
        Log a screenshot capture event.
        
        Args:
            filename: Screenshot filename
        """
        self.logger.info(f"Screenshot captured: {filename}")
    
    def log_upload(self, method: str, filename: str, success: bool) -> None:
        """
        Log an upload event.
        
        Args:
            method: Upload method (email, ftp, cloud)
            filename: Uploaded filename
            success: Whether upload was successful
        """
        status = "SUCCESS" if success else "FAILED"
        self.logger.info(f"Upload {status}: {method} - {filename}")
    
    def log_error(self, error: Exception, context: str = None) -> None:
        """
        Log an error event.
        
        Args:
            error: Exception object
            context: Additional context information
        """
        message = f"Error: {str(error)}"
        if context:
            message = f"{context} - {message}"
        self.logger.error(message, exc_info=True)
    
    def log_system_info(self, info: dict) -> None:
        """
        Log system information.
        
        Args:
            info: System information dictionary
        """
        self.logger.info(f"System Info: {info}")
    
    def get_session_id(self) -> str:
        """Get current session ID."""
        return self.session_id
