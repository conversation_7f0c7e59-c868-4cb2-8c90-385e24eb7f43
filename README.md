# Advanced Professional Keylogger

A comprehensive, feature-rich keylogger with encryption, stealth capabilities, multiple output formats, and cloud integration.

## ⚠️ Legal Disclaimer

This software is intended for educational purposes and legitimate system monitoring only. Users must:
- Only use on systems they own or have explicit permission to monitor
- Comply with all applicable local, state, and federal laws
- Respect privacy rights and obtain proper consent when required
- Use responsibly and ethically

The developers are not responsible for any misuse of this software.

## 🚀 Features

### Core Functionality
- **Advanced Keylogging**: Capture keystrokes with timestamp and window context
- **Multiple Output Formats**: JSON, CSV, TXT, and encrypted formats
- **Real-time Encryption**: AES-256 encryption for sensitive data
- **Smart Filtering**: Filter passwords and sensitive information
- **Session Management**: Organized logging with session tracking

### Stealth & Security
- **Stealth Mode**: Hide console windows and disguise process names
- **Auto-startup**: Automatic startup via registry or startup folder
- **Process Hiding**: Configurable process name masquerading
- **Admin Detection**: Detect and handle administrator privileges

### Data Collection
- **Screenshot Capture**: Automated screenshot capture with compression
- **System Information**: Comprehensive system and hardware details
- **Network Monitoring**: Network interface and connection tracking
- **Performance Monitoring**: Real-time resource usage tracking

### Upload & Sync
- **Email Integration**: Automated email reports with attachments
- **FTP Upload**: Secure FTP file transfer capabilities
- **Cloud Storage**: Dropbox and Google Drive integration
- **Multiple Methods**: Simultaneous upload to multiple destinations

### Monitoring & Analytics
- **Performance Metrics**: CPU, memory, and disk usage monitoring
- **Session Statistics**: Detailed activity and usage analytics
- **Error Tracking**: Comprehensive error logging and reporting
- **Health Monitoring**: System health status and warnings

## 📋 Requirements

### System Requirements
- Python 3.7 or higher
- Windows 10/11 (primary support)
- macOS 10.14+ (limited support)
- Linux Ubuntu 18.04+ (limited support)

### Dependencies
All dependencies are listed in `requirements.txt`. Install with:
```bash
pip install -r requirements.txt
```

## 🛠️ Installation

1. **Clone the repository:**
   ```bash
   git clone https://github.com/yourusername/Keylogger-Script.git
   cd Keylogger-Script
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure the application:**
   ```bash
   # Edit config.yaml with your settings
   ```

4. **Run the keylogger:**
   ```bash
   python main.py
   ```

## ⚙️ Configuration

The keylogger is configured via `config.yaml`. Key sections include:

### General Settings
```yaml
general:
  debug: false
  stealth_mode: true
  auto_start: false
  session_timeout: 3600
```

### Logging Configuration
```yaml
logging:
  log_directory: "logs"
  log_format: "json"  # json, csv, txt, encrypted
  include_timestamps: true
  compress_logs: true
```

### Email Setup
```yaml
email:
  enabled: true
  smtp_server: "smtp.gmail.com"
  smtp_port: 587
  sender_email: "<EMAIL>"
  sender_password: "your-app-password"
  recipient_email: "<EMAIL>"
```

## 🔧 Usage

### Basic Usage
```bash
# Start with default configuration
python main.py

# Start in debug mode
python main.py --debug
```

## 📁 Project Structure

```
Keylogger-Script/
├── main.py                 # Main entry point
├── config.yaml            # Configuration file
├── requirements.txt        # Python dependencies
├── src/                   # Source code
│   ├── keylogger.py       # Main keylogger class
│   ├── config.py          # Configuration management
│   ├── data_manager.py    # Data handling and formats
│   ├── screenshot_capture.py  # Screenshot functionality
│   ├── system_info.py     # System information collection
│   ├── stealth.py         # Stealth and persistence
│   ├── monitoring.py      # Performance monitoring
│   ├── utils/             # Utility modules
│   └── uploaders/         # Upload modules
├── logs/                  # Log files (created at runtime)
└── screenshots/           # Screenshots (created at runtime)
```

## 🔐 Security Features

### Encryption
- **AES-256 Encryption**: Military-grade encryption for all sensitive data
- **Key Management**: Automatic key generation and secure storage
- **Password Protection**: Optional password-based key derivation

### Stealth Capabilities
- **Process Masquerading**: Disguise as system processes
- **Console Hiding**: Hide application windows
- **Registry Integration**: Seamless Windows integration

### Data Protection
- **Smart Filtering**: Automatically filter sensitive information
- **Secure Deletion**: Secure file deletion after upload
- **Memory Protection**: Minimize sensitive data in memory

## 📊 Monitoring & Analytics

### Performance Metrics
- Real-time CPU and memory usage
- Disk space monitoring
- Network activity tracking
- Error rate monitoring

### Session Analytics
- Keystrokes per minute/hour
- Most active applications
- Usage patterns and trends
- System health status

## 🔄 Upload Methods

### Email
- SMTP integration with major providers
- Attachment compression
- Batch file uploads

### FTP
- Secure FTP transfers
- Directory management
- Resume capability

### Cloud Storage
- **Dropbox**: Direct API integration
- **Google Drive**: OAuth2 authentication
- **Automatic Sync**: Background uploads

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## ⚡ Quick Start

1. **Install and configure:**
   ```bash
   git clone https://github.com/yourusername/Keylogger-Script.git
   cd Keylogger-Script
   pip install -r requirements.txt
   ```

2. **Edit configuration:**
   ```bash
   # Configure email settings in config.yaml
   email:
     enabled: true
     sender_email: "<EMAIL>"
     sender_password: "your-app-password"
     recipient_email: "<EMAIL>"
   ```

3. **Run:**
   ```bash
   python main.py
   ```

4. **Stop with Escape key** or Ctrl+C

---

**Remember**: Always use this software responsibly and in compliance with applicable laws and regulations.
